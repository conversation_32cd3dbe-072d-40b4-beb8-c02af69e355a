using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using GraphicTool.Core.DataStructures;
using GraphicTool.Core.FileReaders;

namespace GraphicTool.UI.Controls
{
    /// <summary>
    /// 图档浏览器控件
    /// </summary>
    public partial class GraphicBrowser : UserControl
    {
        #region 事件定义

        /// <summary>
        /// 图像选择事件
        /// </summary>
        public event EventHandler<ImageSelectedEventArgs> ImageSelected;

        /// <summary>
        /// 精灵选择事件
        /// </summary>
        public event EventHandler<SpriteSelectedEventArgs> SpriteSelected;

        #endregion

        #region 私有字段

        /// <summary>
        /// 树视图控件
        /// </summary>
        private TreeView _treeView;

        /// <summary>
        /// 列表视图控件
        /// </summary>
        private ListView _listView;

        /// <summary>
        /// 分割容器
        /// </summary>
        private SplitContainer _splitter;

        /// <summary>
        /// 搜索文本框
        /// </summary>
        private TextBox _searchBox;

        /// <summary>
        /// 视图模式组合框
        /// </summary>
        private ComboBox _viewModeCombo;

        /// <summary>
        /// 图档文件读取器
        /// </summary>
        private GraphicFileReader _graphicReader;

        /// <summary>
        /// 精灵文件读取器
        /// </summary>
        private SpriteFileReader _spriteReader;

        /// <summary>
        /// 当前视图模式
        /// </summary>
        private BrowserViewMode _currentViewMode;

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public GraphicBrowser()
        {
            InitializeComponent();
            _currentViewMode = BrowserViewMode.ByType;
        }

        #endregion

        #region 公共属性

        /// <summary>
        /// 图档文件读取器
        /// </summary>
        public GraphicFileReader GraphicReader
        {
            get { return _graphicReader; }
            set
            {
                _graphicReader = value;
                RefreshContent();
            }
        }

        /// <summary>
        /// 精灵文件读取器
        /// </summary>
        public SpriteFileReader SpriteReader
        {
            get { return _spriteReader; }
            set
            {
                _spriteReader = value;
                RefreshContent();
            }
        }

        /// <summary>
        /// 当前视图模式
        /// </summary>
        public BrowserViewMode ViewMode
        {
            get { return _currentViewMode; }
            set
            {
                _currentViewMode = value;
                RefreshContent();
            }
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // 创建顶部工具栏
            CreateToolbar();
            
            // 创建主分割容器
            CreateMainSplitter();
            
            // 创建树视图
            CreateTreeView();
            
            // 创建列表视图
            CreateListView();
            
            this.ResumeLayout(false);
        }

        /// <summary>
        /// 创建工具栏
        /// </summary>
        private void CreateToolbar()
        {
            var toolbar = new Panel();
            toolbar.Height = 30;
            toolbar.Dock = DockStyle.Top;
            toolbar.BackColor = SystemColors.Control;
            
            // 搜索标签
            var searchLabel = new Label();
            searchLabel.Text = "搜索:";
            searchLabel.Location = new Point(5, 7);
            searchLabel.Size = new Size(40, 16);
            
            // 搜索文本框
            _searchBox = new TextBox();
            _searchBox.Location = new Point(50, 5);
            _searchBox.Size = new Size(150, 20);
            _searchBox.TextChanged += SearchBox_TextChanged;
            
            // 视图模式标签
            var viewLabel = new Label();
            viewLabel.Text = "视图:";
            viewLabel.Location = new Point(210, 7);
            viewLabel.Size = new Size(35, 16);
            
            // 视图模式组合框
            _viewModeCombo = new ComboBox();
            _viewModeCombo.Location = new Point(250, 5);
            _viewModeCombo.Size = new Size(100, 21);
            _viewModeCombo.DropDownStyle = ComboBoxStyle.DropDownList;
            _viewModeCombo.Items.AddRange(new object[] { "按类型", "按版本", "按大小" });
            _viewModeCombo.SelectedIndex = 0;
            _viewModeCombo.SelectedIndexChanged += ViewModeCombo_SelectedIndexChanged;
            
            toolbar.Controls.AddRange(new Control[] { searchLabel, _searchBox, viewLabel, _viewModeCombo });
            this.Controls.Add(toolbar);
        }

        /// <summary>
        /// 创建主分割容器
        /// </summary>
        private void CreateMainSplitter()
        {
            _splitter = new SplitContainer();
            _splitter.Dock = DockStyle.Fill;
            _splitter.Orientation = Orientation.Horizontal;
            _splitter.SplitterDistance = 200;
            _splitter.Panel1MinSize = 100;
            _splitter.Panel2MinSize = 100;
            
            this.Controls.Add(_splitter);
        }

        /// <summary>
        /// 创建树视图
        /// </summary>
        private void CreateTreeView()
        {
            _treeView = new TreeView();
            _treeView.Dock = DockStyle.Fill;
            _treeView.HideSelection = false;
            _treeView.ShowLines = true;
            _treeView.ShowPlusMinus = true;
            _treeView.ShowRootLines = true;
            _treeView.AfterSelect += TreeView_AfterSelect;
            
            _splitter.Panel1.Controls.Add(_treeView);
        }

        /// <summary>
        /// 创建列表视图
        /// </summary>
        private void CreateListView()
        {
            _listView = new ListView();
            _listView.Dock = DockStyle.Fill;
            _listView.View = View.Details;
            _listView.FullRowSelect = true;
            _listView.GridLines = true;
            _listView.HideSelection = false;
            _listView.MultiSelect = false;
            
            // 添加列
            _listView.Columns.Add("编号", 80);
            _listView.Columns.Add("尺寸", 80);
            _listView.Columns.Add("大小", 80);
            _listView.Columns.Add("格式", 60);
            _listView.Columns.Add("地址", 100);
            
            _listView.SelectedIndexChanged += ListView_SelectedIndexChanged;
            _listView.DoubleClick += ListView_DoubleClick;
            
            _splitter.Panel2.Controls.Add(_listView);
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 刷新内容
        /// </summary>
        public void RefreshContent()
        {
            _treeView.Nodes.Clear();
            _listView.Items.Clear();
            
            if (_graphicReader != null && _graphicReader.IsInitialized)
            {
                LoadGraphicContent();
            }
            
            if (_spriteReader != null && _spriteReader.IsInitialized)
            {
                LoadSpriteContent();
            }
        }

        /// <summary>
        /// 搜索项目
        /// </summary>
        /// <param name="searchText">搜索文本</param>
        public void SearchItems(string searchText)
        {
            if (string.IsNullOrEmpty(searchText))
            {
                RefreshContent();
                return;
            }
            
            // 过滤列表视图项目
            _listView.BeginUpdate();
            
            foreach (ListViewItem item in _listView.Items)
            {
                var visible = item.Text.IndexOf(searchText, StringComparison.OrdinalIgnoreCase) >= 0 ||
                             item.SubItems.Cast<ListViewItem.ListViewSubItem>()
                                 .Any(sub => sub.Text.IndexOf(searchText, StringComparison.OrdinalIgnoreCase) >= 0);
                
                item.BackColor = visible ? SystemColors.Window : SystemColors.Control;
                item.ForeColor = visible ? SystemColors.WindowText : SystemColors.GrayText;
            }
            
            _listView.EndUpdate();
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 加载图档内容
        /// </summary>
        private void LoadGraphicContent()
        {
            var graphicNode = new TreeNode("图档文件");
            graphicNode.Tag = "Graphics";
            
            switch (_currentViewMode)
            {
                case BrowserViewMode.ByType:
                    LoadGraphicsByType(graphicNode);
                    break;
                case BrowserViewMode.ByVersion:
                    LoadGraphicsByVersion(graphicNode);
                    break;
                case BrowserViewMode.BySize:
                    LoadGraphicsBySize(graphicNode);
                    break;
            }
            
            _treeView.Nodes.Add(graphicNode);
            graphicNode.Expand();
        }

        /// <summary>
        /// 按类型加载图档
        /// </summary>
        /// <param name="parentNode">父节点</param>
        private void LoadGraphicsByType(TreeNode parentNode)
        {
            var imageCount = _graphicReader.LoadedImageCount;
            
            // 按大小分类
            var smallImages = new TreeNode("小图像 (≤64x64)");
            var mediumImages = new TreeNode("中图像 (65-256x256)");
            var largeImages = new TreeNode("大图像 (>256x256)");
            
            smallImages.Tag = "SmallImages";
            mediumImages.Tag = "MediumImages";
            largeImages.Tag = "LargeImages";
            
            parentNode.Nodes.AddRange(new TreeNode[] { smallImages, mediumImages, largeImages });
        }

        /// <summary>
        /// 按版本加载图档
        /// </summary>
        /// <param name="parentNode">父节点</param>
        private void LoadGraphicsByVersion(TreeNode parentNode)
        {
            var supportedModes = _graphicReader.SupportedModes;
            
            foreach (var mode in supportedModes)
            {
                var modeNode = new TreeNode(GetModeDisplayName(mode));
                modeNode.Tag = mode;
                parentNode.Nodes.Add(modeNode);
            }
        }

        /// <summary>
        /// 按大小加载图档
        /// </summary>
        /// <param name="parentNode">父节点</param>
        private void LoadGraphicsBySize(TreeNode parentNode)
        {
            // 按文件大小分类
            var sizes = new[] { "< 1KB", "1-10KB", "10-100KB", "> 100KB" };
            
            foreach (var size in sizes)
            {
                var sizeNode = new TreeNode(size);
                sizeNode.Tag = size;
                parentNode.Nodes.Add(sizeNode);
            }
        }

        /// <summary>
        /// 加载精灵内容
        /// </summary>
        private void LoadSpriteContent()
        {
            var spriteNode = new TreeNode("精灵文件");
            spriteNode.Tag = "Sprites";
            
            var spriteCount = _spriteReader.LoadedSpriteCount;
            spriteNode.Text = string.Format("精灵文件 ({0})", spriteCount);
            
            _treeView.Nodes.Add(spriteNode);
        }

        /// <summary>
        /// 加载图像列表
        /// </summary>
        /// <param name="category">分类</param>
        private void LoadImageList(string category)
        {
            _listView.Items.Clear();
            
            if (_graphicReader == null || !_graphicReader.IsInitialized)
                return;
            
            var imageCount = _graphicReader.LoadedImageCount;
            
            for (int i = 0; i < Math.Min(imageCount, 1000); i++) // 限制显示数量
            {
                try
                {
                    var imageInfo = _graphicReader.GetImageInfo(i);
                    
                    if (ShouldIncludeImage(imageInfo, category))
                    {
                        var item = new ListViewItem(i.ToString());
                        item.SubItems.Add(string.Format("{0}x{1}", imageInfo.Width, imageInfo.Height));
                        item.SubItems.Add(FormatFileSize(imageInfo.Size));
                        item.SubItems.Add(GetModeDisplayName(imageInfo.GetBinMode()));
                        item.SubItems.Add(string.Format("0x{0:X8}", imageInfo.Address));
                        item.Tag = i;
                        
                        _listView.Items.Add(item);
                    }
                }
                catch (Exception)
                {
                    // 忽略无效的图像
                }
            }
        }

        /// <summary>
        /// 判断是否应该包含图像
        /// </summary>
        /// <param name="imageInfo">图像信息</param>
        /// <param name="category">分类</param>
        /// <returns>是否包含</returns>
        private bool ShouldIncludeImage(AdrnBin imageInfo, string category)
        {
            switch (category)
            {
                case "SmallImages":
                    return imageInfo.Width <= 64 && imageInfo.Height <= 64;
                case "MediumImages":
                    return (imageInfo.Width > 64 && imageInfo.Width <= 256) || 
                           (imageInfo.Height > 64 && imageInfo.Height <= 256);
                case "LargeImages":
                    return imageInfo.Width > 256 || imageInfo.Height > 256;
                default:
                    return true;
            }
        }

        /// <summary>
        /// 获取模式显示名称
        /// </summary>
        /// <param name="mode">文件模式</param>
        /// <returns>显示名称</returns>
        private string GetModeDisplayName(Enums.BinMode mode)
        {
            switch (mode)
            {
                case Enums.BinMode.Normal: return "标准版";
                case Enums.BinMode.Ex: return "扩展版";
                case Enums.BinMode.Ver2: return "第二版";
                case Enums.BinMode.Joy: return "Joy版";
                case Enums.BinMode.JoyCh: return "Joy中文版";
                default: return mode.ToString();
            }
        }

        /// <summary>
        /// 格式化文件大小
        /// </summary>
        /// <param name="size">字节大小</param>
        /// <returns>格式化的大小</returns>
        private string FormatFileSize(uint size)
        {
            if (size < 1024)
                return size + " B";
            else if (size < 1024 * 1024)
                return (size / 1024.0).ToString("F1") + " KB";
            else
                return (size / (1024.0 * 1024.0)).ToString("F1") + " MB";
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 搜索框文本改变事件
        /// </summary>
        private void SearchBox_TextChanged(object sender, EventArgs e)
        {
            SearchItems(_searchBox.Text);
        }

        /// <summary>
        /// 视图模式改变事件
        /// </summary>
        private void ViewModeCombo_SelectedIndexChanged(object sender, EventArgs e)
        {
            _currentViewMode = (BrowserViewMode)_viewModeCombo.SelectedIndex;
            RefreshContent();
        }

        /// <summary>
        /// 树视图选择事件
        /// </summary>
        private void TreeView_AfterSelect(object sender, TreeViewEventArgs e)
        {
            if (e.Node.Tag != null)
            {
                LoadImageList(e.Node.Tag.ToString());
            }
        }

        /// <summary>
        /// 列表视图选择事件
        /// </summary>
        private void ListView_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (_listView.SelectedItems.Count > 0)
            {
                var item = _listView.SelectedItems[0];
                var imageIndex = (int)item.Tag;
                
                OnImageSelected(new ImageSelectedEventArgs(imageIndex));
            }
        }

        /// <summary>
        /// 列表视图双击事件
        /// </summary>
        private void ListView_DoubleClick(object sender, EventArgs e)
        {
            if (_listView.SelectedItems.Count > 0)
            {
                var item = _listView.SelectedItems[0];
                var imageIndex = (int)item.Tag;
                
                // 触发图像预览事件
                OnImageSelected(new ImageSelectedEventArgs(imageIndex, true));
            }
        }

        #endregion

        #region 事件触发

        /// <summary>
        /// 触发图像选择事件
        /// </summary>
        /// <param name="e">事件参数</param>
        protected virtual void OnImageSelected(ImageSelectedEventArgs e)
        {
            if (ImageSelected != null)
            ImageSelected.Invoke(this, e);
        }

        /// <summary>
        /// 触发精灵选择事件
        /// </summary>
        /// <param name="e">事件参数</param>
        protected virtual void OnSpriteSelected(SpriteSelectedEventArgs e)
        {
            if (SpriteSelected != null)
            SpriteSelected.Invoke(this, e);
        }

        #endregion
    }

    #region 枚举和事件参数

    /// <summary>
    /// 浏览器视图模式
    /// </summary>
    public enum BrowserViewMode
    {
        ByType = 0,
        ByVersion = 1,
        BySize = 2
    }

    /// <summary>
    /// 图像选择事件参数
    /// </summary>
    public class ImageSelectedEventArgs : EventArgs
    {
        public int ImageIndex { get; private set; }
        public bool IsDoubleClick { get; private set; }

        public ImageSelectedEventArgs(int imageIndex, bool isDoubleClick = false)
        {
            ImageIndex = imageIndex;
            IsDoubleClick = isDoubleClick;
        }
    }

    /// <summary>
    /// 精灵选择事件参数
    /// </summary>
    public class SpriteSelectedEventArgs : EventArgs
    {
        public int SpriteIndex { get; private set; }
        public bool IsDoubleClick { get; private set; }

        public SpriteSelectedEventArgs(int spriteIndex, bool isDoubleClick = false)
        {
            SpriteIndex = spriteIndex;
            IsDoubleClick = isDoubleClick;
        }
    }

    #endregion
}
