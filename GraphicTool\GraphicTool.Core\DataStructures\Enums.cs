using System;

namespace GraphicTool.Core.DataStructures
{
    /// <summary>
    /// 项目中使用的枚举定义
    /// </summary>
    public static class Enums
    {
        /// <summary>
        /// 文件模式枚举
        /// 对应原始C++代码中的BINMODE枚举
        /// </summary>
        public enum BinMode
        {
            /// <summary>
            /// 基础版本
            /// </summary>
            Normal = 0,

            /// <summary>
            /// 扩展版本
            /// </summary>
            Ex = 1,

            /// <summary>
            /// 第二版本
            /// </summary>
            Ver2 = 2,

            /// <summary>
            /// Joy版本（台湾版）
            /// </summary>
            Joy = 3,

            /// <summary>
            /// Joy中文版本
            /// </summary>
            JoyCh = 4,

            /// <summary>
            /// 最大值（用于数组大小和验证）
            /// </summary>
            Max = 5
        }

        /// <summary>
        /// 图像格式枚举
        /// </summary>
        public enum ImageFormat
        {
            /// <summary>
            /// Windows位图格式
            /// </summary>
            BMP,

            /// <summary>
            /// 便携式网络图形格式
            /// </summary>
            PNG,

            /// <summary>
            /// Targa图像格式
            /// </summary>
            TGA,

            /// <summary>
            /// JPEG压缩格式
            /// </summary>
            JPG,

            /// <summary>
            /// DirectDraw Surface格式
            /// </summary>
            DDS,

            /// <summary>
            /// 原始RD格式
            /// </summary>
            RD
        }

        /// <summary>
        /// 压缩级别枚举
        /// </summary>
        public enum CompressionLevel
        {
            /// <summary>
            /// 无压缩
            /// </summary>
            None = 0,

            /// <summary>
            /// 低压缩
            /// </summary>
            Low = 1,

            /// <summary>
            /// 中等压缩
            /// </summary>
            Medium = 2,

            /// <summary>
            /// 高压缩
            /// </summary>
            High = 3,

            /// <summary>
            /// 最高压缩
            /// </summary>
            Maximum = 4
        }

        /// <summary>
        /// 导出格式枚举
        /// </summary>
        public enum ExportFormat
        {
            /// <summary>
            /// 单个图像文件
            /// </summary>
            SingleImage,

            /// <summary>
            /// 图像序列
            /// </summary>
            ImageSequence,

            /// <summary>
            /// 精灵表
            /// </summary>
            SpriteSheet,

            /// <summary>
            /// 动画GIF
            /// </summary>
            AnimatedGif,

            /// <summary>
            /// 视频文件
            /// </summary>
            Video
        }

        /// <summary>
        /// 动画播放模式枚举
        /// </summary>
        public enum AnimationPlayMode
        {
            /// <summary>
            /// 单次播放
            /// </summary>
            Once,

            /// <summary>
            /// 循环播放
            /// </summary>
            Loop,

            /// <summary>
            /// 往返播放
            /// </summary>
            PingPong,

            /// <summary>
            /// 反向播放
            /// </summary>
            Reverse
        }

        /// <summary>
        /// 缩放模式枚举
        /// </summary>
        public enum ScaleMode
        {
            /// <summary>
            /// 最近邻插值
            /// </summary>
            NearestNeighbor,

            /// <summary>
            /// 双线性插值
            /// </summary>
            Bilinear,

            /// <summary>
            /// 双三次插值
            /// </summary>
            Bicubic,

            /// <summary>
            /// 高质量双三次插值
            /// </summary>
            HighQualityBicubic
        }

        /// <summary>
        /// 文件操作结果枚举
        /// </summary>
        public enum FileOperationResult
        {
            /// <summary>
            /// 成功
            /// </summary>
            Success,

            /// <summary>
            /// 文件不存在
            /// </summary>
            FileNotFound,

            /// <summary>
            /// 访问被拒绝
            /// </summary>
            AccessDenied,

            /// <summary>
            /// 格式不支持
            /// </summary>
            UnsupportedFormat,

            /// <summary>
            /// 数据损坏
            /// </summary>
            CorruptedData,

            /// <summary>
            /// 内存不足
            /// </summary>
            OutOfMemory,

            /// <summary>
            /// 未知错误
            /// </summary>
            UnknownError
        }
    }
}
