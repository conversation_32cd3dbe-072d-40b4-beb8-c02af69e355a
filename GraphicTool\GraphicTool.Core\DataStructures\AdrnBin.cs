using System;
using System.Runtime.InteropServices;

namespace GraphicTool.Core.DataStructures
{
    /// <summary>
    /// 地址信息结构
    /// 对应原始C++代码中的ADRNBIN结构
    /// 用于存储图像在文件中的位置和基本信息
    /// </summary>
    [StructLayout(LayoutKind.Sequential, Pack = 1)]
    public struct AdrnBin
    {
        /// <summary>
        /// 位图编号
        /// </summary>
        public uint BitmapNo;

        /// <summary>
        /// 在文件中的地址偏移
        /// </summary>
        public uint Address;

        /// <summary>
        /// 数据大小（字节）
        /// </summary>
        public uint Size;

        /// <summary>
        /// X轴偏移量
        /// </summary>
        public short XOffset;

        /// <summary>
        /// Y轴偏移量
        /// </summary>
        public short YOffset;

        /// <summary>
        /// 图像宽度
        /// </summary>
        public ushort Width;

        /// <summary>
        /// 图像高度
        /// </summary>
        public ushort Height;

        /// <summary>
        /// 文件模式（对应BinMode枚举）
        /// </summary>
        public short BinMode;

        /// <summary>
        /// 地图属性
        /// </summary>
        public MapAttr Attributes;

        /// <summary>
        /// 检查数据是否有效
        /// </summary>
        public bool IsValid
        {
            get
            {
                return Width > 0 && Height > 0 && Size > 0 && 
                       BinMode >= 0 && BinMode < (int)Enums.BinMode.Max;
            }
        }

        /// <summary>
        /// 获取BinMode枚举值
        /// </summary>
        public Enums.BinMode GetBinMode()
        {
            if (BinMode >= 0 && BinMode < (int)Enums.BinMode.Max)
                return (Enums.BinMode)BinMode;
            return Enums.BinMode.Normal;
        }

        /// <summary>
        /// 设置BinMode
        /// </summary>
        /// <param name="mode">BinMode枚举值</param>
        public void SetBinMode(Enums.BinMode mode)
        {
            BinMode = (short)mode;
        }

        /// <summary>
        /// 创建一个新的地址信息
        /// </summary>
        /// <param name="bitmapNo">位图编号</param>
        /// <param name="address">文件地址</param>
        /// <param name="size">数据大小</param>
        /// <param name="width">宽度</param>
        /// <param name="height">高度</param>
        /// <param name="binMode">文件模式</param>
        /// <param name="xOffset">X偏移</param>
        /// <param name="yOffset">Y偏移</param>
        /// <returns>地址信息结构</returns>
        public static AdrnBin Create(uint bitmapNo, uint address, uint size, 
            ushort width, ushort height, Enums.BinMode binMode = Enums.BinMode.Normal,
            short xOffset = 0, short yOffset = 0)
        {
            return new AdrnBin
            {
                BitmapNo = bitmapNo,
                Address = address,
                Size = size,
                XOffset = xOffset,
                YOffset = yOffset,
                Width = width,
                Height = height,
                BinMode = (short)binMode,
                Attributes = new MapAttr()
            };
        }

        /// <summary>
        /// 获取结构体的字节大小
        /// </summary>
        public static int SizeOf { get { return Marshal.SizeOf(typeof(AdrnBin)); } }

        public override string ToString()
        {
            return string.Format("AdrnBin: No={0}, {1}x{2}, Addr=0x{3:X8}, Size={4}, Mode={5}",
                BitmapNo, Width, Height, Address, Size, GetBinMode());
        }
    }
}
