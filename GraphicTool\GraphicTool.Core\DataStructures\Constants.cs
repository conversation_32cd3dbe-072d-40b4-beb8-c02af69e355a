using System;

namespace GraphicTool.Core.DataStructures
{
    /// <summary>
    /// 项目中使用的常量定义
    /// </summary>
    public static class Constants
    {
        #region 文件格式常量

        /// <summary>
        /// RD文件格式标识符
        /// </summary>
        public static readonly byte[] RD_SIGNATURE = { (byte)'R', (byte)'D' };

        /// <summary>
        /// 最大支持的图像数量
        /// </summary>
        public const int MAX_GRAPHICS = 100000;

        /// <summary>
        /// 最大支持的精灵数量
        /// </summary>
        public const int MAX_SPRITES = 50000;

        /// <summary>
        /// 最大支持的动画数量
        /// </summary>
        public const int MAX_ANIMATIONS = 1000;

        /// <summary>
        /// 最大支持的帧数量
        /// </summary>
        public const int MAX_FRAMES = 100;

        #endregion

        #region 压缩标志常量

        /// <summary>
        /// 压缩标志：未压缩
        /// </summary>
        public const byte COMPRESS_FLAG_NONE = 0x00;

        /// <summary>
        /// 压缩标志：压缩
        /// </summary>
        public const byte COMPRESS_FLAG_COMPRESSED = 0x01;

        /// <summary>
        /// 压缩标志：包含调色板
        /// </summary>
        public const byte COMPRESS_FLAG_PALETTE = 0x02;

        /// <summary>
        /// 压缩标志：压缩且包含调色板
        /// </summary>
        public const byte COMPRESS_FLAG_COMPRESSED_PALETTE = 0x03;

        /// <summary>
        /// 压缩位标志
        /// </summary>
        public const byte BIT_CMP = 0x80;

        #endregion

        #region 文件扩展名常量

        /// <summary>
        /// 图档文件扩展名
        /// </summary>
        public const string GRAPHIC_BIN_EXT = ".bin";

        /// <summary>
        /// 图档信息文件扩展名
        /// </summary>
        public const string GRAPHIC_INFO_EXT = ".bin";

        /// <summary>
        /// 精灵文件扩展名
        /// </summary>
        public const string SPRITE_BIN_EXT = ".bin";

        /// <summary>
        /// 精灵信息文件扩展名
        /// </summary>
        public const string SPRITE_INFO_EXT = ".bin";

        #endregion

        #region 默认文件名常量

        /// <summary>
        /// 默认图档文件名模式
        /// </summary>
        public static readonly string[] DEFAULT_GRAPHIC_NAMES = 
        {
            "Graphic.bin",      // Normal
            "GraphicEx.bin",    // Ex
            "GraphicVer2.bin",  // Ver2
            "GraphicJoy.bin",   // Joy
            "GraphicJoyCh.bin"  // JoyCh
        };

        /// <summary>
        /// 默认图档信息文件名模式
        /// </summary>
        public static readonly string[] DEFAULT_GRAPHIC_INFO_NAMES = 
        {
            "GraphicInfo.bin",      // Normal
            "GraphicInfoEx.bin",    // Ex
            "GraphicInfoVer2.bin",  // Ver2
            "GraphicInfoJoy.bin",   // Joy
            "GraphicInfoJoyCh.bin"  // JoyCh
        };

        /// <summary>
        /// 默认精灵文件名模式
        /// </summary>
        public static readonly string[] DEFAULT_SPRITE_NAMES = 
        {
            "Spr.bin",      // Normal
            "SprEx.bin",    // Ex
            "SprVer2.bin",  // Ver2
            "SprJoy.bin",   // Joy
            "SprJoyCh.bin"  // JoyCh
        };

        /// <summary>
        /// 默认精灵信息文件名模式
        /// </summary>
        public static readonly string[] DEFAULT_SPRITE_INFO_NAMES = 
        {
            "SprAdrn.bin",      // Normal
            "SprAdrnEx.bin",    // Ex
            "SprAdrnVer2.bin",  // Ver2
            "SprAdrnJoy.bin",   // Joy
            "SprAdrnJoyCh.bin"  // JoyCh
        };

        #endregion

        #region 默认配置常量

        /// <summary>
        /// 默认缓存大小（MB）
        /// </summary>
        public const int DEFAULT_CACHE_SIZE_MB = 256;

        /// <summary>
        /// 默认缩略图大小
        /// </summary>
        public const int DEFAULT_THUMBNAIL_SIZE = 64;

        /// <summary>
        /// 默认动画帧率（FPS）
        /// </summary>
        public const int DEFAULT_ANIMATION_FPS = 10;

        /// <summary>
        /// 默认导出质量（0-100）
        /// </summary>
        public const int DEFAULT_EXPORT_QUALITY = 90;

        /// <summary>
        /// 最大图像尺寸
        /// </summary>
        public const int MAX_IMAGE_SIZE = 4096;

        /// <summary>
        /// 最小图像尺寸
        /// </summary>
        public const int MIN_IMAGE_SIZE = 1;

        #endregion

        #region 调色板常量

        /// <summary>
        /// 标准调色板大小（颜色数量）
        /// </summary>
        public const int STANDARD_PALETTE_SIZE = 256;

        /// <summary>
        /// 调色板字节大小（RGB）
        /// </summary>
        public const int PALETTE_BYTE_SIZE = STANDARD_PALETTE_SIZE * 3;

        /// <summary>
        /// 调色板字节大小（RGBA）
        /// </summary>
        public const int PALETTE_RGBA_BYTE_SIZE = STANDARD_PALETTE_SIZE * 4;

        /// <summary>
        /// 透明色索引
        /// </summary>
        public const byte TRANSPARENT_COLOR_INDEX = 0;

        #endregion

        #region 特效标志常量

        /// <summary>
        /// 特效：无
        /// </summary>
        public const byte EFFECT_NONE = 0x00;

        /// <summary>
        /// 特效：闪烁
        /// </summary>
        public const byte EFFECT_BLINK = 0x01;

        /// <summary>
        /// 特效：渐变
        /// </summary>
        public const byte EFFECT_FADE = 0x02;

        /// <summary>
        /// 特效：旋转
        /// </summary>
        public const byte EFFECT_ROTATE = 0x04;

        /// <summary>
        /// 特效：缩放
        /// </summary>
        public const byte EFFECT_SCALE = 0x08;

        /// <summary>
        /// 特效：颜色变换
        /// </summary>
        public const byte EFFECT_COLOR_TRANSFORM = 0x10;

        #endregion

        #region 错误消息常量

        /// <summary>
        /// 文件不存在错误消息
        /// </summary>
        public const string ERROR_FILE_NOT_FOUND = "指定的文件不存在";

        /// <summary>
        /// 格式不支持错误消息
        /// </summary>
        public const string ERROR_UNSUPPORTED_FORMAT = "不支持的文件格式";

        /// <summary>
        /// 数据损坏错误消息
        /// </summary>
        public const string ERROR_CORRUPTED_DATA = "文件数据已损坏";

        /// <summary>
        /// 内存不足错误消息
        /// </summary>
        public const string ERROR_OUT_OF_MEMORY = "内存不足";

        /// <summary>
        /// 访问被拒绝错误消息
        /// </summary>
        public const string ERROR_ACCESS_DENIED = "文件访问被拒绝";

        #endregion

        #region 版本信息常量

        /// <summary>
        /// 工具版本号
        /// </summary>
        public const string TOOL_VERSION = "1.0.0";

        /// <summary>
        /// 支持的最低文件格式版本
        /// </summary>
        public const byte MIN_SUPPORTED_FORMAT_VERSION = 1;

        /// <summary>
        /// 支持的最高文件格式版本
        /// </summary>
        public const byte MAX_SUPPORTED_FORMAT_VERSION = 3;

        #endregion
    }
}
