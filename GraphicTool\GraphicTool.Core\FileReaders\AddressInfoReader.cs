using System;
using System.Collections.Generic;
using System.IO;
using System.Runtime.InteropServices;
using GraphicTool.Core.DataStructures;

namespace GraphicTool.Core.FileReaders
{
    /// <summary>
    /// 地址信息读取器
    /// 负责读取和解析图档地址信息文件
    /// </summary>
    public static class AddressInfoReader
    {
        /// <summary>
        /// 从文件读取地址信息列表
        /// </summary>
        /// <param name="filePath">地址信息文件路径</param>
        /// <param name="mode">文件模式</param>
        /// <returns>地址信息列表</returns>
        public static List<AdrnBin> ReadAddressInfo(string filePath, Enums.BinMode mode)
        {
            if (string.IsNullOrEmpty(filePath))
            {
                throw new ArgumentNullException("filePath");
            }

            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException("地址信息文件不存在: " + filePath);
            }

            var addressList = new List<AdrnBin>();

            try
            {
                using (var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.Read))
                {
                    addressList = ReadAddressInfo(fileStream, mode);
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("读取地址信息文件失败: " + ex.Message, ex);
            }

            return addressList;
        }

        /// <summary>
        /// 从流读取地址信息列表
        /// </summary>
        /// <param name="stream">文件流</param>
        /// <param name="mode">文件模式</param>
        /// <returns>地址信息列表</returns>
        public static List<AdrnBin> ReadAddressInfo(Stream stream, Enums.BinMode mode)
        {
            if (stream == null)
            {
                throw new ArgumentNullException("stream");
            }

            if (!stream.CanRead)
            {
                throw new ArgumentException("流不支持读取操作");
            }

            var addressList = new List<AdrnBin>();
            stream.Seek(0, SeekOrigin.Begin);

            // 根据不同版本使用不同的读取方法
            switch (mode)
            {
                case Enums.BinMode.Normal:
                case Enums.BinMode.Ex:
                    addressList = ReadStandardFormat(stream, mode);
                    break;
                case Enums.BinMode.Ver2:
                    addressList = ReadVer2Format(stream, mode);
                    break;
                case Enums.BinMode.Joy:
                case Enums.BinMode.JoyCh:
                    addressList = ReadJoyFormat(stream, mode);
                    break;
                default:
                    throw new NotSupportedException("不支持的文件模式: " + mode);
            }

            return addressList;
        }

        /// <summary>
        /// 读取标准格式的地址信息
        /// </summary>
        /// <param name="stream">文件流</param>
        /// <param name="mode">文件模式</param>
        /// <returns>地址信息列表</returns>
        private static List<AdrnBin> ReadStandardFormat(Stream stream, Enums.BinMode mode)
        {
            var addressList = new List<AdrnBin>();
            var buffer = new byte[AdrnBin.SizeOf];

            while (stream.Read(buffer, 0, buffer.Length) == buffer.Length)
            {
                var adrnBin = BytesToAdrnBin(buffer);
                adrnBin.SetBinMode(mode);

                // 验证数据有效性
                if (adrnBin.IsValid)
                {
                    addressList.Add(adrnBin);
                }
            }

            return addressList;
        }

        /// <summary>
        /// 读取Ver2格式的地址信息
        /// </summary>
        /// <param name="stream">文件流</param>
        /// <param name="mode">文件模式</param>
        /// <returns>地址信息列表</returns>
        private static List<AdrnBin> ReadVer2Format(Stream stream, Enums.BinMode mode)
        {
            // Ver2格式可能有额外的头部信息
            var addressList = new List<AdrnBin>();
            
            // 跳过可能的头部信息
            var headerBuffer = new byte[16];
            stream.Read(headerBuffer, 0, headerBuffer.Length);
            
            // 检查是否是有效的头部
            if (IsValidVer2Header(headerBuffer))
            {
                // 继续读取地址信息
                return ReadStandardFormat(stream, mode);
            }
            else
            {
                // 回到开始位置，按标准格式读取
                stream.Seek(0, SeekOrigin.Begin);
                return ReadStandardFormat(stream, mode);
            }
        }

        /// <summary>
        /// 读取Joy格式的地址信息
        /// </summary>
        /// <param name="stream">文件流</param>
        /// <param name="mode">文件模式</param>
        /// <returns>地址信息列表</returns>
        private static List<AdrnBin> ReadJoyFormat(Stream stream, Enums.BinMode mode)
        {
            // Joy格式可能有不同的字节序或结构
            var addressList = new List<AdrnBin>();
            var buffer = new byte[AdrnBin.SizeOf];

            while (stream.Read(buffer, 0, buffer.Length) == buffer.Length)
            {
                var adrnBin = BytesToAdrnBinJoy(buffer);
                adrnBin.SetBinMode(mode);

                // 验证数据有效性
                if (adrnBin.IsValid)
                {
                    addressList.Add(adrnBin);
                }
            }

            return addressList;
        }

        /// <summary>
        /// 字节数组转换为AdrnBin结构（标准格式）
        /// </summary>
        /// <param name="buffer">字节数组</param>
        /// <returns>AdrnBin结构</returns>
        public static AdrnBin BytesToAdrnBin(byte[] buffer)
        {
            if (buffer == null || buffer.Length < AdrnBin.SizeOf)
            {
                throw new ArgumentException("缓冲区大小不足");
            }

            var adrnBin = new AdrnBin();
            
            // 使用BitConverter读取各个字段
            int offset = 0;
            
            adrnBin.BitmapNo = BitConverter.ToUInt32(buffer, offset);
            offset += 4;
            
            adrnBin.Address = BitConverter.ToUInt32(buffer, offset);
            offset += 4;
            
            adrnBin.Size = BitConverter.ToUInt32(buffer, offset);
            offset += 4;
            
            adrnBin.XOffset = BitConverter.ToInt16(buffer, offset);
            offset += 2;
            
            adrnBin.YOffset = BitConverter.ToInt16(buffer, offset);
            offset += 2;
            
            adrnBin.Width = BitConverter.ToUInt16(buffer, offset);
            offset += 2;
            
            adrnBin.Height = BitConverter.ToUInt16(buffer, offset);
            offset += 2;
            
            adrnBin.BinMode = BitConverter.ToInt16(buffer, offset);
            offset += 2;
            
            // 读取MapAttr结构
            adrnBin.Attributes = BytesToMapAttr(buffer, offset);

            return adrnBin;
        }

        /// <summary>
        /// 字节数组转换为AdrnBin结构（Joy格式）
        /// </summary>
        /// <param name="buffer">字节数组</param>
        /// <returns>AdrnBin结构</returns>
        private static AdrnBin BytesToAdrnBinJoy(byte[] buffer)
        {
            // Joy格式可能需要字节序转换
            var adrnBin = BytesToAdrnBin(buffer);
            
            // 如果需要，在这里进行字节序转换
            // adrnBin.BitmapNo = SwapBytes(adrnBin.BitmapNo);
            
            return adrnBin;
        }

        /// <summary>
        /// 字节数组转换为MapAttr结构
        /// </summary>
        /// <param name="buffer">字节数组</param>
        /// <param name="offset">偏移量</param>
        /// <returns>MapAttr结构</returns>
        private static MapAttr BytesToMapAttr(byte[] buffer, int offset)
        {
            var mapAttr = new MapAttr();
            
            if (buffer.Length >= offset + MapAttr.SizeOf)
            {
                mapAttr.Flags = BitConverter.ToUInt32(buffer, offset);
                offset += 4;
                
                mapAttr.ExtAttr1 = BitConverter.ToUInt32(buffer, offset);
                offset += 4;
                
                mapAttr.ExtAttr2 = BitConverter.ToUInt32(buffer, offset);
                offset += 4;
                
                mapAttr.Reserved = BitConverter.ToUInt32(buffer, offset);
            }
            
            return mapAttr;
        }

        /// <summary>
        /// 检查是否是有效的Ver2头部
        /// </summary>
        /// <param name="headerBuffer">头部缓冲区</param>
        /// <returns>是否有效</returns>
        private static bool IsValidVer2Header(byte[] headerBuffer)
        {
            // 检查特定的标识符或版本号
            // 这里需要根据实际的Ver2格式来实现
            return false; // 暂时返回false
        }

        /// <summary>
        /// 验证地址信息的有效性
        /// </summary>
        /// <param name="adrnBin">地址信息</param>
        /// <returns>是否有效</returns>
        public static bool ValidateAddressInfo(AdrnBin adrnBin)
        {
            // 检查基本有效性
            if (!adrnBin.IsValid)
            {
                return false;
            }

            // 检查图像尺寸是否合理
            if (adrnBin.Width > Constants.MAX_IMAGE_SIZE || adrnBin.Height > Constants.MAX_IMAGE_SIZE)
            {
                return false;
            }

            // 检查数据大小是否合理
            if (adrnBin.Size == 0 || adrnBin.Size > 10 * 1024 * 1024) // 最大10MB
            {
                return false;
            }

            return true;
        }

        /// <summary>
        /// 创建地址信息索引
        /// </summary>
        /// <param name="addressList">地址信息列表</param>
        /// <returns>按位图编号索引的字典</returns>
        public static Dictionary<uint, AdrnBin> CreateIndex(List<AdrnBin> addressList)
        {
            var index = new Dictionary<uint, AdrnBin>();
            
            if (addressList != null)
            {
                foreach (var adrnBin in addressList)
                {
                    if (ValidateAddressInfo(adrnBin))
                    {
                        index[adrnBin.BitmapNo] = adrnBin;
                    }
                }
            }
            
            return index;
        }
    }
}
