using System;
using System.Collections.Generic;
using System.IO;
using GraphicTool.Core.DataStructures;

namespace GraphicTool.Core.FileReaders
{
    /// <summary>
    /// 精灵文件读取器
    /// 负责读取和管理精灵动画文件
    /// </summary>
    public class SpriteFileReader : IDisposable
    {
        #region 私有字段

        /// <summary>
        /// 精灵文件流字典，按BinMode索引
        /// </summary>
        private Dictionary<Enums.BinMode, FileStream> _spriteStreams;

        /// <summary>
        /// 精灵数据列表
        /// </summary>
        private List<SpriteData> _spriteData;

        /// <summary>
        /// 精灵索引字典，按精灵编号索引
        /// </summary>
        private Dictionary<uint, int> _spriteIndex;

        /// <summary>
        /// 是否已初始化
        /// </summary>
        private bool _isInitialized;

        /// <summary>
        /// 是否已释放资源
        /// </summary>
        private bool _disposed;

        #endregion

        #region 公共属性

        /// <summary>
        /// 是否已初始化
        /// </summary>
        public bool IsInitialized
        {
            get { return _isInitialized; }
        }

        /// <summary>
        /// 已加载的精灵数量
        /// </summary>
        public int LoadedSpriteCount
        {
            get { return _spriteData != null ? _spriteData.Count : 0; }
        }

        /// <summary>
        /// 支持的文件模式列表
        /// </summary>
        public List<Enums.BinMode> SupportedModes
        {
            get
            {
                var modes = new List<Enums.BinMode>();
                if (_spriteStreams != null)
                {
                    foreach (var kvp in _spriteStreams)
                    {
                        if (kvp.Value != null)
                            modes.Add(kvp.Key);
                    }
                }
                return modes;
            }
        }

        #endregion

        #region 构造函数和初始化

        /// <summary>
        /// 构造函数
        /// </summary>
        public SpriteFileReader()
        {
            _spriteStreams = new Dictionary<Enums.BinMode, FileStream>();
            _spriteData = new List<SpriteData>();
            _spriteIndex = new Dictionary<uint, int>();
            _isInitialized = false;
            _disposed = false;
        }

        /// <summary>
        /// 加载精灵文件
        /// </summary>
        /// <param name="spritePaths">精灵文件路径数组</param>
        /// <param name="spriteInfoPaths">精灵信息文件路径数组</param>
        /// <returns>加载是否成功</returns>
        public bool LoadSpriteFiles(string[] spritePaths, string[] spriteInfoPaths)
        {
            try
            {
                if (_isInitialized)
                {
                    throw new InvalidOperationException("SpriteFileReader已经初始化");
                }

                if (spritePaths == null || spriteInfoPaths == null)
                {
                    throw new ArgumentNullException("文件路径数组不能为null");
                }

                if (spritePaths.Length != spriteInfoPaths.Length)
                {
                    throw new ArgumentException("精灵文件和信息文件数量不匹配");
                }

                // 打开精灵文件
                for (int i = 0; i < spritePaths.Length && i < (int)Enums.BinMode.Max; i++)
                {
                    var mode = (Enums.BinMode)i;
                    
                    if (!string.IsNullOrEmpty(spritePaths[i]) && File.Exists(spritePaths[i]))
                    {
                        var spriteStream = new FileStream(spritePaths[i], FileMode.Open, FileAccess.Read, FileShare.Read);
                        _spriteStreams[mode] = spriteStream;
                    }
                }

                // 读取精灵信息文件
                for (int i = 0; i < spriteInfoPaths.Length && i < (int)Enums.BinMode.Max; i++)
                {
                    var mode = (Enums.BinMode)i;
                    
                    if (!string.IsNullOrEmpty(spriteInfoPaths[i]) && File.Exists(spriteInfoPaths[i]))
                    {
                        LoadSpriteInfo(spriteInfoPaths[i], mode);
                    }
                }

                // 检查是否至少有一个文件成功打开
                if (_spriteStreams.Count == 0)
                {
                    throw new FileNotFoundException("没有找到有效的精灵文件");
                }

                // 创建索引
                CreateSpriteIndex();

                _isInitialized = true;
                return true;
            }
            catch (Exception ex)
            {
                // 清理已打开的文件
                Cleanup();
                throw new InvalidOperationException("加载精灵文件失败: " + ex.Message, ex);
            }
        }

        /// <summary>
        /// 使用默认文件名加载
        /// </summary>
        /// <param name="basePath">基础路径</param>
        /// <returns>加载是否成功</returns>
        public bool LoadWithDefaults(string basePath)
        {
            if (string.IsNullOrEmpty(basePath))
            {
                throw new ArgumentNullException("basePath");
            }

            var spritePaths = new string[(int)Enums.BinMode.Max];
            var spriteInfoPaths = new string[(int)Enums.BinMode.Max];

            for (int i = 0; i < (int)Enums.BinMode.Max; i++)
            {
                spritePaths[i] = Path.Combine(basePath, Constants.DEFAULT_SPRITE_NAMES[i]);
                spriteInfoPaths[i] = Path.Combine(basePath, Constants.DEFAULT_SPRITE_INFO_NAMES[i]);
            }

            return LoadSpriteFiles(spritePaths, spriteInfoPaths);
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 获取精灵数据
        /// </summary>
        /// <param name="spriteNo">精灵编号</param>
        /// <returns>精灵数据，如果不存在返回null</returns>
        public SpriteData? GetSpriteData(int spriteNo)
        {
            if (!_isInitialized)
            {
                throw new InvalidOperationException("SpriteFileReader未初始化");
            }

            if (_spriteIndex.ContainsKey((uint)spriteNo))
            {
                var index = _spriteIndex[(uint)spriteNo];
                if (index >= 0 && index < _spriteData.Count)
                {
                    return _spriteData[index];
                }
            }

            return null;
        }

        /// <summary>
        /// 获取动画帧列表
        /// </summary>
        /// <param name="spriteNo">精灵编号</param>
        /// <param name="animIndex">动画索引</param>
        /// <returns>动画帧列表</returns>
        public List<AnimFrame> GetAnimationFrames(int spriteNo, int animIndex)
        {
            var spriteData = GetSpriteData(spriteNo);
            if (spriteData.HasValue)
            {
                var anim = spriteData.Value.GetAnimation(animIndex);
                if (anim.HasValue)
                {
                    return anim.Value.Frames ?? new List<AnimFrame>();
                }
            }

            return new List<AnimFrame>();
        }

        /// <summary>
        /// 获取所有精灵编号
        /// </summary>
        /// <returns>精灵编号列表</returns>
        public List<uint> GetAllSpriteNumbers()
        {
            return new List<uint>(_spriteIndex.Keys);
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 加载精灵信息
        /// </summary>
        /// <param name="infoFilePath">信息文件路径</param>
        /// <param name="mode">文件模式</param>
        private void LoadSpriteInfo(string infoFilePath, Enums.BinMode mode)
        {
            using (var fileStream = new FileStream(infoFilePath, FileMode.Open, FileAccess.Read, FileShare.Read))
            {
                // 读取精灵索引信息
                var buffer = new byte[12]; // 精灵索引结构大小
                
                while (fileStream.Read(buffer, 0, buffer.Length) == buffer.Length)
                {
                    var spriteData = ParseSpriteInfo(buffer, mode);
                    if (spriteData.IsValid)
                    {
                        _spriteData.Add(spriteData);
                    }
                }
            }
        }

        /// <summary>
        /// 解析精灵信息
        /// </summary>
        /// <param name="buffer">数据缓冲区</param>
        /// <param name="mode">文件模式</param>
        /// <returns>精灵数据</returns>
        private SpriteData ParseSpriteInfo(byte[] buffer, Enums.BinMode mode)
        {
            var spriteData = new SpriteData();
            
            // 解析精灵索引数据
            spriteData.SprNo = BitConverter.ToUInt32(buffer, 0);
            spriteData.Offset = BitConverter.ToUInt32(buffer, 4);
            spriteData.AnimSize = BitConverter.ToUInt32(buffer, 8);
            spriteData.Format = 1; // 默认格式
            spriteData.Animations = new List<AnimList>();
            
            return spriteData;
        }

        /// <summary>
        /// 读取动画数据
        /// </summary>
        /// <param name="spriteData">精灵数据</param>
        /// <returns>是否成功</returns>
        public bool ReadAnimationData(ref SpriteData spriteData)
        {
            if (!_isInitialized)
            {
                return false;
            }

            // 确定使用哪个文件流
            FileStream spriteStream = null;
            foreach (var kvp in _spriteStreams)
            {
                if (kvp.Value != null)
                {
                    spriteStream = kvp.Value;
                    break;
                }
            }

            if (spriteStream == null)
            {
                return false;
            }

            try
            {
                // 定位到动画数据位置
                spriteStream.Seek(spriteData.Offset, SeekOrigin.Begin);
                
                // 读取动画列表
                spriteData.Animations = new List<AnimList>();
                
                for (int i = 0; i < spriteData.AnimSize; i++)
                {
                    var animList = ReadAnimList(spriteStream);
                    if (animList.IsValid)
                    {
                        spriteData.Animations.Add(animList);
                    }
                }

                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// 读取动画列表
        /// </summary>
        /// <param name="stream">文件流</param>
        /// <returns>动画列表</returns>
        private AnimList ReadAnimList(FileStream stream)
        {
            var animList = new AnimList();
            var buffer = new byte[20]; // 动画头部大小
            
            if (stream.Read(buffer, 0, buffer.Length) == buffer.Length)
            {
                // 解析动画头部
                animList.Direction = buffer[0];
                animList.AnimNo = buffer[1];
                animList.FrameCount = buffer[2];
                animList.PlayMode = buffer[3];
                
                // 读取帧数据
                animList.Frames = new List<AnimFrame>();
                
                for (int i = 0; i < animList.FrameCount; i++)
                {
                    var frame = ReadAnimFrame(stream);
                    animList.Frames.Add(frame);
                }
            }
            
            return animList;
        }

        /// <summary>
        /// 读取动画帧
        /// </summary>
        /// <param name="stream">文件流</param>
        /// <returns>动画帧</returns>
        private AnimFrame ReadAnimFrame(FileStream stream)
        {
            var frame = new AnimFrame();
            var buffer = new byte[10]; // 帧数据大小
            
            if (stream.Read(buffer, 0, buffer.Length) == buffer.Length)
            {
                frame.GraphicNo = BitConverter.ToUInt32(buffer, 0);
                frame.XOffset = BitConverter.ToInt16(buffer, 4);
                frame.YOffset = BitConverter.ToInt16(buffer, 6);
                frame.Duration = (uint)BitConverter.ToUInt16(buffer, 8) * 10; // 转换为毫秒
                frame.Alpha = 255; // 默认不透明
                frame.ScaleX = 100; // 默认原始大小
                frame.ScaleY = 100;
            }
            
            return frame;
        }

        /// <summary>
        /// 创建精灵索引
        /// </summary>
        private void CreateSpriteIndex()
        {
            _spriteIndex.Clear();
            
            for (int i = 0; i < _spriteData.Count; i++)
            {
                var spriteData = _spriteData[i];
                _spriteIndex[spriteData.SprNo] = i;
            }
        }

        /// <summary>
        /// 清理资源
        /// </summary>
        private void Cleanup()
        {
            if (_spriteStreams != null)
            {
                foreach (var stream in _spriteStreams.Values)
                {
                    if (stream != null)
                    {
                        stream.Dispose();
                    }
                }
                _spriteStreams.Clear();
            }

            if (_spriteData != null)
            {
                _spriteData.Clear();
            }

            if (_spriteIndex != null)
            {
                _spriteIndex.Clear();
            }

            _isInitialized = false;
        }

        #endregion

        #region IDisposable实现

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        /// <param name="disposing">是否正在释放托管资源</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    Cleanup();
                }
                _disposed = true;
            }
        }

        /// <summary>
        /// 析构函数
        /// </summary>
        ~SpriteFileReader()
        {
            Dispose(false);
        }

        #endregion
    }
}
