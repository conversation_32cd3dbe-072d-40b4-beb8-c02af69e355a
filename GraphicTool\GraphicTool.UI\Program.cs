using System;
using System.IO;
using System.Text;
using System.Windows.Forms;

namespace GraphicTool.UI
{
    /// <summary>
    /// 程序入口点
    /// </summary>
    static class Program
    {
        private static string logFile = "ui_error_log.txt";

        /// <summary>
        /// 应用程序的主入口点
        /// </summary>
        [STAThread]
        static void Main()
        {
            try
            {
                Log("=== UI程序启动 ===");
                Log("时间: " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));

                // 设置全局异常处理
                Application.ThreadException += Application_ThreadException;
                AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;

                Log("设置异常处理器完成");

                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);

                Log("开始创建主窗口...");
                var mainForm = new MainForm();
                Log("主窗口创建成功");

                Log("开始运行应用程序...");
                Application.Run(mainForm);
                Log("应用程序正常退出");
            }
            catch (Exception ex)
            {
                Log("Main方法异常: " + ex.GetType().Name);
                Log("异常消息: " + ex.Message);
                Log("异常堆栈: " + ex.StackTrace);

                MessageBox.Show("程序启动失败: " + ex.Message + "\n\n详细信息已记录到: " + logFile,
                    "启动错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private static void Application_ThreadException(object sender, System.Threading.ThreadExceptionEventArgs e)
        {
            Log("UI线程异常: " + e.Exception.GetType().Name);
            Log("异常消息: " + e.Exception.Message);
            Log("异常堆栈: " + e.Exception.StackTrace);

            MessageBox.Show("UI异常: " + e.Exception.Message + "\n\n详细信息已记录到: " + logFile,
                "UI错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        private static void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            var ex = e.ExceptionObject as Exception;
            if (ex != null)
            {
                Log("未处理异常: " + ex.GetType().Name);
                Log("异常消息: " + ex.Message);
                Log("异常堆栈: " + ex.StackTrace);
            }
            else
            {
                Log("未知类型的未处理异常: " + e.ExceptionObject.ToString());
            }
        }

        private static void Log(string message)
        {
            try
            {
                var logMessage = DateTime.Now.ToString("HH:mm:ss.fff") + " - " + message;
                File.AppendAllText(logFile, logMessage + Environment.NewLine, Encoding.UTF8);
            }
            catch
            {
                // 忽略日志写入错误
            }
        }
    }
}
