using System;
using System.IO;
using GraphicTool.Core.DataStructures;
using GraphicTool.Core.FileReaders;
using GraphicTool.Core.Compression;

namespace GraphicTool.Tests
{
    /// <summary>
    /// 文件读取器测试类
    /// </summary>
    public static class FileReaderTests
    {
        /// <summary>
        /// 运行所有文件读取器测试
        /// </summary>
        public static void RunAllTests()
        {
            Console.WriteLine("=== 开始文件读取器测试 ===");
            Console.WriteLine();

            TestAddressInfoReader();
            TestRDDecompressor();
            TestGraphicFileReader();
            TestSpriteFileReader();

            Console.WriteLine();
            Console.WriteLine("=== 文件读取器测试完成 ===");
        }

        /// <summary>
        /// 测试地址信息读取器
        /// </summary>
        private static void TestAddressInfoReader()
        {
            Console.WriteLine("测试 AddressInfoReader:");

            try
            {
                // 创建测试数据
                var testData = CreateTestAddressData();
                
                // 测试字节数组转换
                var adrnBin = AddressInfoReader.BytesToAdrnBin(testData);
                Console.WriteLine("  字节数组转换: " + adrnBin);
                Console.WriteLine("  数据有效性: " + adrnBin.IsValid);
                
                // 测试验证功能
                var isValid = AddressInfoReader.ValidateAddressInfo(adrnBin);
                Console.WriteLine("  验证结果: " + isValid);
                
                // 测试索引创建
                var addressList = new System.Collections.Generic.List<AdrnBin> { adrnBin };
                var index = AddressInfoReader.CreateIndex(addressList);
                Console.WriteLine("  索引创建: " + index.Count + " 项");
            }
            catch (Exception ex)
            {
                Console.WriteLine("  测试失败: " + ex.Message);
            }

            Console.WriteLine();
        }

        /// <summary>
        /// 测试RD解压缩器
        /// </summary>
        private static void TestRDDecompressor()
        {
            Console.WriteLine("测试 RDDecompressor:");

            try
            {
                // 创建测试RD数据
                var testRDData = CreateTestRDData();
                
                Console.WriteLine("  测试数据大小: " + testRDData.Length + " 字节");
                
                // 测试解压缩（这里会失败，因为是模拟数据）
                try
                {
                    var decompressed = RDDecompressor.Decompress(testRDData);
                    Console.WriteLine("  解压缩成功: " + decompressed.Length + " 字节");
                }
                catch (Exception ex)
                {
                    Console.WriteLine("  解压缩测试（预期失败）: " + ex.Message);
                }
                
                // 测试位图创建
                var testImageData = new byte[64 * 64]; // 64x64 灰度图像
                for (int i = 0; i < testImageData.Length; i++)
                {
                    testImageData[i] = (byte)(i % 256);
                }
                
                var testPalette = CreateTestPalette();
                var bitmap = RDDecompressor.CreateBitmap(testImageData, 64, 64, testPalette);
                Console.WriteLine("  位图创建: " + bitmap.Width + "x" + bitmap.Height);
                bitmap.Dispose();
            }
            catch (Exception ex)
            {
                Console.WriteLine("  测试失败: " + ex.Message);
            }

            Console.WriteLine();
        }

        /// <summary>
        /// 测试图档文件读取器
        /// </summary>
        private static void TestGraphicFileReader()
        {
            Console.WriteLine("测试 GraphicFileReader:");

            try
            {
                using (var reader = new GraphicFileReader())
                {
                    Console.WriteLine("  创建读取器: 成功");
                    Console.WriteLine("  初始化状态: " + reader.IsInitialized);
                    Console.WriteLine("  图像数量: " + reader.LoadedImageCount);
                    Console.WriteLine("  支持模式: " + reader.SupportedModes.Count);
                    
                    // 测试初始化（会失败，因为没有实际文件）
                    try
                    {
                        var graphicPaths = new string[] { "test1.bin", "test2.bin" };
                        var infoPaths = new string[] { "info1.bin", "info2.bin" };
                        reader.Initialize(graphicPaths, infoPaths);
                        Console.WriteLine("  初始化: 成功");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine("  初始化测试（预期失败）: " + ex.Message);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("  测试失败: " + ex.Message);
            }

            Console.WriteLine();
        }

        /// <summary>
        /// 测试精灵文件读取器
        /// </summary>
        private static void TestSpriteFileReader()
        {
            Console.WriteLine("测试 SpriteFileReader:");

            try
            {
                using (var reader = new SpriteFileReader())
                {
                    Console.WriteLine("  创建读取器: 成功");
                    Console.WriteLine("  初始化状态: " + reader.IsInitialized);
                    Console.WriteLine("  精灵数量: " + reader.LoadedSpriteCount);
                    Console.WriteLine("  支持模式: " + reader.SupportedModes.Count);
                    
                    // 测试加载（会失败，因为没有实际文件）
                    try
                    {
                        var spritePaths = new string[] { "spr1.bin", "spr2.bin" };
                        var spriteInfoPaths = new string[] { "sprinfo1.bin", "spriteinfo2.bin" };
                        reader.LoadSpriteFiles(spritePaths, spriteInfoPaths);
                        Console.WriteLine("  加载: 成功");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine("  加载测试（预期失败）: " + ex.Message);
                    }
                    
                    // 测试获取精灵数据
                    var spriteData = reader.GetSpriteData(0);
                    Console.WriteLine("  获取精灵数据: " + (spriteData.HasValue ? "有数据" : "无数据"));
                    
                    // 测试获取动画帧
                    var frames = reader.GetAnimationFrames(0, 0);
                    Console.WriteLine("  获取动画帧: " + frames.Count + " 帧");
                    
                    // 测试获取所有精灵编号
                    var spriteNumbers = reader.GetAllSpriteNumbers();
                    Console.WriteLine("  精灵编号列表: " + spriteNumbers.Count + " 个");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("  测试失败: " + ex.Message);
            }

            Console.WriteLine();
        }

        /// <summary>
        /// 创建测试地址数据
        /// </summary>
        /// <returns>测试数据</returns>
        private static byte[] CreateTestAddressData()
        {
            var buffer = new byte[AdrnBin.SizeOf];
            int offset = 0;
            
            // BitmapNo
            BitConverter.GetBytes((uint)12345).CopyTo(buffer, offset);
            offset += 4;
            
            // Address
            BitConverter.GetBytes((uint)0x1000).CopyTo(buffer, offset);
            offset += 4;
            
            // Size
            BitConverter.GetBytes((uint)2048).CopyTo(buffer, offset);
            offset += 4;
            
            // XOffset
            BitConverter.GetBytes((short)10).CopyTo(buffer, offset);
            offset += 2;
            
            // YOffset
            BitConverter.GetBytes((short)20).CopyTo(buffer, offset);
            offset += 2;
            
            // Width
            BitConverter.GetBytes((ushort)64).CopyTo(buffer, offset);
            offset += 2;
            
            // Height
            BitConverter.GetBytes((ushort)64).CopyTo(buffer, offset);
            offset += 2;
            
            // BinMode
            BitConverter.GetBytes((short)1).CopyTo(buffer, offset);
            offset += 2;
            
            // MapAttr (16 bytes)
            BitConverter.GetBytes((uint)0x04).CopyTo(buffer, offset); // Flags
            offset += 4;
            BitConverter.GetBytes((uint)0).CopyTo(buffer, offset); // ExtAttr1
            offset += 4;
            BitConverter.GetBytes((uint)0).CopyTo(buffer, offset); // ExtAttr2
            offset += 4;
            BitConverter.GetBytes((uint)0).CopyTo(buffer, offset); // Reserved
            
            return buffer;
        }

        /// <summary>
        /// 创建测试RD数据
        /// </summary>
        /// <returns>测试RD数据</returns>
        private static byte[] CreateTestRDData()
        {
            var buffer = new byte[RDHeader.SizeOf + 100]; // 头部 + 一些数据
            int offset = 0;
            
            // RD标识
            buffer[offset++] = (byte)'R';
            buffer[offset++] = (byte)'D';
            
            // 压缩标志
            buffer[offset++] = Constants.COMPRESS_FLAG_NONE;
            
            // 宽度
            BitConverter.GetBytes((uint)32).CopyTo(buffer, offset);
            offset += 4;
            
            // 高度
            BitConverter.GetBytes((uint)32).CopyTo(buffer, offset);
            offset += 4;
            
            // 大小
            BitConverter.GetBytes((uint)100).CopyTo(buffer, offset);
            offset += 4;
            
            // 填充一些测试数据
            for (int i = offset; i < buffer.Length; i++)
            {
                buffer[i] = (byte)(i % 256);
            }
            
            return buffer;
        }

        /// <summary>
        /// 创建测试调色板
        /// </summary>
        /// <returns>测试调色板</returns>
        private static byte[] CreateTestPalette()
        {
            var palette = new byte[Constants.PALETTE_BYTE_SIZE];
            
            for (int i = 0; i < 256; i++)
            {
                palette[i * 3] = (byte)i;     // R
                palette[i * 3 + 1] = (byte)(255 - i); // G
                palette[i * 3 + 2] = (byte)(i / 2);   // B
            }
            
            return palette;
        }
    }
}
