using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using GraphicTool.Core.DataStructures;

namespace GraphicTool.Core.ImageProcessing
{
    /// <summary>
    /// 图像验证器
    /// 负责验证图像格式、完整性和有效性
    /// </summary>
    public static class ImageValidator
    {
        /// <summary>
        /// 验证图像格式
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>验证结果</returns>
        public static Enums.FileOperationResult ValidateImageFormat(string filePath)
        {
            if (string.IsNullOrEmpty(filePath))
            {
                return Enums.FileOperationResult.FileNotFound;
            }

            if (!File.Exists(filePath))
            {
                return Enums.FileOperationResult.FileNotFound;
            }

            try
            {
                var extension = Path.GetExtension(filePath).ToLowerInvariant();
                
                switch (extension)
                {
                    case ".bmp":
                        return ValidateBmpFormat(filePath);
                    case ".png":
                        return ValidatePngFormat(filePath);
                    case ".jpg":
                    case ".jpeg":
                        return ValidateJpegFormat(filePath);
                    case ".tga":
                        return ValidateTgaFormat(filePath);
                    case ".bin":
                        return ValidateRdFormat(filePath);
                    default:
                        return Enums.FileOperationResult.UnsupportedFormat;
                }
            }
            catch (UnauthorizedAccessException)
            {
                return Enums.FileOperationResult.AccessDenied;
            }
            catch (OutOfMemoryException)
            {
                return Enums.FileOperationResult.OutOfMemory;
            }
            catch (Exception)
            {
                return Enums.FileOperationResult.CorruptedData;
            }
        }

        /// <summary>
        /// 验证图像完整性
        /// </summary>
        /// <param name="bitmap">位图对象</param>
        /// <returns>验证结果</returns>
        public static Enums.FileOperationResult ValidateImageIntegrity(Bitmap bitmap)
        {
            if (bitmap == null)
            {
                return Enums.FileOperationResult.CorruptedData;
            }

            try
            {
                // 检查基本属性
                if (bitmap.Width <= 0 || bitmap.Height <= 0)
                {
                    return Enums.FileOperationResult.CorruptedData;
                }

                // 检查尺寸限制
                if (bitmap.Width > Constants.MAX_IMAGE_SIZE || bitmap.Height > Constants.MAX_IMAGE_SIZE)
                {
                    return Enums.FileOperationResult.UnsupportedFormat;
                }

                // 检查像素格式
                if (!IsSupportedPixelFormat(bitmap.PixelFormat))
                {
                    return Enums.FileOperationResult.UnsupportedFormat;
                }

                // 尝试访问像素数据
                var testColor = bitmap.GetPixel(0, 0);
                
                return Enums.FileOperationResult.Success;
            }
            catch (ArgumentOutOfRangeException)
            {
                return Enums.FileOperationResult.CorruptedData;
            }
            catch (InvalidOperationException)
            {
                return Enums.FileOperationResult.CorruptedData;
            }
            catch (OutOfMemoryException)
            {
                return Enums.FileOperationResult.OutOfMemory;
            }
            catch (Exception)
            {
                return Enums.FileOperationResult.UnknownError;
            }
        }

        /// <summary>
        /// 验证尺寸限制
        /// </summary>
        /// <param name="width">宽度</param>
        /// <param name="height">高度</param>
        /// <returns>是否有效</returns>
        public static bool ValidateDimensions(int width, int height)
        {
            if (width < Constants.MIN_IMAGE_SIZE || height < Constants.MIN_IMAGE_SIZE)
            {
                return false;
            }

            if (width > Constants.MAX_IMAGE_SIZE || height > Constants.MAX_IMAGE_SIZE)
            {
                return false;
            }

            return true;
        }

        /// <summary>
        /// 验证调色板
        /// </summary>
        /// <param name="palette">调色板数据</param>
        /// <returns>是否有效</returns>
        public static bool ValidatePalette(byte[] palette)
        {
            return PaletteManager.ValidatePalette(palette);
        }

        /// <summary>
        /// 验证RD格式数据
        /// </summary>
        /// <param name="data">RD数据</param>
        /// <returns>验证结果</returns>
        public static Enums.FileOperationResult ValidateRdData(byte[] data)
        {
            if (data == null || data.Length < RDHeader.SizeOf)
            {
                return Enums.FileOperationResult.CorruptedData;
            }

            try
            {
                // 检查RD标识
                if (data[0] != (byte)'R' || data[1] != (byte)'D')
                {
                    return Enums.FileOperationResult.UnsupportedFormat;
                }

                // 解析头部
                var width = BitConverter.ToUInt32(data, 3);
                var height = BitConverter.ToUInt32(data, 7);
                var size = BitConverter.ToUInt32(data, 11);

                // 验证尺寸
                if (!ValidateDimensions((int)width, (int)height))
                {
                    return Enums.FileOperationResult.UnsupportedFormat;
                }

                // 验证数据大小
                if (size == 0 || size > data.Length - RDHeader.SizeOf)
                {
                    return Enums.FileOperationResult.CorruptedData;
                }

                return Enums.FileOperationResult.Success;
            }
            catch (Exception)
            {
                return Enums.FileOperationResult.CorruptedData;
            }
        }

        /// <summary>
        /// 检查文件是否可读
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否可读</returns>
        public static bool IsFileReadable(string filePath)
        {
            if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
            {
                return false;
            }

            try
            {
                using (var stream = File.OpenRead(filePath))
                {
                    return stream.CanRead;
                }
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// 检查文件是否可写
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否可写</returns>
        public static bool IsFileWritable(string filePath)
        {
            try
            {
                if (File.Exists(filePath))
                {
                    // 检查现有文件是否可写
                    var attributes = File.GetAttributes(filePath);
                    return (attributes & FileAttributes.ReadOnly) == 0;
                }
                else
                {
                    // 检查目录是否可写
                    var directory = Path.GetDirectoryName(filePath);
                    if (string.IsNullOrEmpty(directory))
                    {
                        directory = Directory.GetCurrentDirectory();
                    }

                    if (!Directory.Exists(directory))
                    {
                        return false;
                    }

                    // 尝试创建临时文件
                    var tempFile = Path.Combine(directory, Path.GetRandomFileName());
                    try
                    {
                        File.WriteAllText(tempFile, "test");
                        File.Delete(tempFile);
                        return true;
                    }
                    catch (Exception)
                    {
                        return false;
                    }
                }
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// 获取图像信息
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>图像信息字符串</returns>
        public static string GetImageInfo(string filePath)
        {
            if (!IsFileReadable(filePath))
            {
                return "文件无法读取";
            }

            try
            {
                using (var bitmap = new Bitmap(filePath))
                {
                    var fileInfo = new FileInfo(filePath);
                    
                    return string.Format(
                        "文件: {0}\n" +
                        "尺寸: {1}x{2}\n" +
                        "像素格式: {3}\n" +
                        "文件大小: {4:N0} 字节\n" +
                        "创建时间: {5}",
                        Path.GetFileName(filePath),
                        bitmap.Width,
                        bitmap.Height,
                        bitmap.PixelFormat,
                        fileInfo.Length,
                        fileInfo.CreationTime);
                }
            }
            catch (Exception ex)
            {
                return "获取图像信息失败: " + ex.Message;
            }
        }

        /// <summary>
        /// 估算内存使用量
        /// </summary>
        /// <param name="width">宽度</param>
        /// <param name="height">高度</param>
        /// <param name="pixelFormat">像素格式</param>
        /// <returns>内存使用量（字节）</returns>
        public static long EstimateMemoryUsage(int width, int height, PixelFormat pixelFormat)
        {
            var bytesPerPixel = GetBytesPerPixel(pixelFormat);
            return (long)width * height * bytesPerPixel;
        }

        #region 私有方法

        /// <summary>
        /// 验证BMP格式
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>验证结果</returns>
        private static Enums.FileOperationResult ValidateBmpFormat(string filePath)
        {
            try
            {
                using (var stream = File.OpenRead(filePath))
                {
                    var header = new byte[2];
                    if (stream.Read(header, 0, 2) != 2)
                    {
                        return Enums.FileOperationResult.CorruptedData;
                    }

                    // 检查BMP标识
                    if (header[0] != (byte)'B' || header[1] != (byte)'M')
                    {
                        return Enums.FileOperationResult.UnsupportedFormat;
                    }
                }

                // 尝试加载位图
                using (var bitmap = new Bitmap(filePath))
                {
                    return ValidateImageIntegrity(bitmap);
                }
            }
            catch (Exception)
            {
                return Enums.FileOperationResult.CorruptedData;
            }
        }

        /// <summary>
        /// 验证PNG格式
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>验证结果</returns>
        private static Enums.FileOperationResult ValidatePngFormat(string filePath)
        {
            try
            {
                using (var stream = File.OpenRead(filePath))
                {
                    var header = new byte[8];
                    if (stream.Read(header, 0, 8) != 8)
                    {
                        return Enums.FileOperationResult.CorruptedData;
                    }

                    // 检查PNG标识
                    var pngSignature = new byte[] { 0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A };
                    for (int i = 0; i < 8; i++)
                    {
                        if (header[i] != pngSignature[i])
                        {
                            return Enums.FileOperationResult.UnsupportedFormat;
                        }
                    }
                }

                // 尝试加载位图
                using (var bitmap = new Bitmap(filePath))
                {
                    return ValidateImageIntegrity(bitmap);
                }
            }
            catch (Exception)
            {
                return Enums.FileOperationResult.CorruptedData;
            }
        }

        /// <summary>
        /// 验证JPEG格式
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>验证结果</returns>
        private static Enums.FileOperationResult ValidateJpegFormat(string filePath)
        {
            try
            {
                using (var stream = File.OpenRead(filePath))
                {
                    var header = new byte[2];
                    if (stream.Read(header, 0, 2) != 2)
                    {
                        return Enums.FileOperationResult.CorruptedData;
                    }

                    // 检查JPEG标识
                    if (header[0] != 0xFF || header[1] != 0xD8)
                    {
                        return Enums.FileOperationResult.UnsupportedFormat;
                    }
                }

                // 尝试加载位图
                using (var bitmap = new Bitmap(filePath))
                {
                    return ValidateImageIntegrity(bitmap);
                }
            }
            catch (Exception)
            {
                return Enums.FileOperationResult.CorruptedData;
            }
        }

        /// <summary>
        /// 验证TGA格式
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>验证结果</returns>
        private static Enums.FileOperationResult ValidateTgaFormat(string filePath)
        {
            // TGA格式验证的简单实现
            try
            {
                using (var stream = File.OpenRead(filePath))
                {
                    if (stream.Length < 18) // TGA头部最小大小
                    {
                        return Enums.FileOperationResult.CorruptedData;
                    }

                    var header = new byte[18];
                    if (stream.Read(header, 0, 18) != 18)
                    {
                        return Enums.FileOperationResult.CorruptedData;
                    }

                    // 简单的TGA格式检查
                    var imageType = header[2];
                    if (imageType != 1 && imageType != 2 && imageType != 3 && imageType != 9 && imageType != 10 && imageType != 11)
                    {
                        return Enums.FileOperationResult.UnsupportedFormat;
                    }

                    return Enums.FileOperationResult.Success;
                }
            }
            catch (Exception)
            {
                return Enums.FileOperationResult.CorruptedData;
            }
        }

        /// <summary>
        /// 验证RD格式文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>验证结果</returns>
        private static Enums.FileOperationResult ValidateRdFormat(string filePath)
        {
            try
            {
                var data = File.ReadAllBytes(filePath);
                return ValidateRdData(data);
            }
            catch (Exception)
            {
                return Enums.FileOperationResult.CorruptedData;
            }
        }

        /// <summary>
        /// 检查是否支持的像素格式
        /// </summary>
        /// <param name="format">像素格式</param>
        /// <returns>是否支持</returns>
        private static bool IsSupportedPixelFormat(PixelFormat format)
        {
            switch (format)
            {
                case PixelFormat.Format8bppIndexed:
                case PixelFormat.Format24bppRgb:
                case PixelFormat.Format32bppRgb:
                case PixelFormat.Format32bppArgb:
                    return true;
                default:
                    return false;
            }
        }

        /// <summary>
        /// 获取每像素字节数
        /// </summary>
        /// <param name="format">像素格式</param>
        /// <returns>每像素字节数</returns>
        private static int GetBytesPerPixel(PixelFormat format)
        {
            switch (format)
            {
                case PixelFormat.Format8bppIndexed:
                    return 1;
                case PixelFormat.Format24bppRgb:
                    return 3;
                case PixelFormat.Format32bppRgb:
                case PixelFormat.Format32bppArgb:
                    return 4;
                default:
                    return 4; // 默认值
            }
        }

        #endregion
    }
}
