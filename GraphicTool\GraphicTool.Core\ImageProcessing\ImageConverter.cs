using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Runtime.InteropServices;
using GraphicTool.Core.DataStructures;

namespace GraphicTool.Core.ImageProcessing
{
    /// <summary>
    /// 图像转换器
    /// 负责图像格式转换和数据处理
    /// </summary>
    public static class ImageConverter
    {
        /// <summary>
        /// 将原始图像数据转换为位图
        /// </summary>
        /// <param name="imageData">图像数据</param>
        /// <param name="width">宽度</param>
        /// <param name="height">高度</param>
        /// <param name="palette">调色板（可选）</param>
        /// <returns>位图对象</returns>
        public static Bitmap ConvertToBitmap(byte[] imageData, int width, int height, byte[] palette = null)
        {
            if (imageData == null)
            {
                throw new ArgumentNullException("imageData");
            }

            if (width <= 0 || height <= 0)
            {
                throw new ArgumentException("图像尺寸必须大于0");
            }

            if (width > Constants.MAX_IMAGE_SIZE || height > Constants.MAX_IMAGE_SIZE)
            {
                throw new ArgumentException("图像尺寸超过最大限制");
            }

            try
            {
                if (palette != null)
                {
                    return CreateIndexedBitmap(imageData, width, height, palette);
                }
                else
                {
                    return CreateTrueColorBitmap(imageData, width, height);
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("转换位图失败: " + ex.Message, ex);
            }
        }

        /// <summary>
        /// 将位图转换为原始数据
        /// </summary>
        /// <param name="bitmap">位图对象</param>
        /// <param name="palette">输出调色板</param>
        /// <returns>原始图像数据</returns>
        public static byte[] ConvertToRawData(Bitmap bitmap, out byte[] palette)
        {
            if (bitmap == null)
            {
                throw new ArgumentNullException("bitmap");
            }

            palette = null;

            try
            {
                switch (bitmap.PixelFormat)
                {
                    case PixelFormat.Format8bppIndexed:
                        return ExtractIndexedData(bitmap, out palette);
                    case PixelFormat.Format24bppRgb:
                    case PixelFormat.Format32bppRgb:
                    case PixelFormat.Format32bppArgb:
                        return ExtractTrueColorData(bitmap);
                    default:
                        // 转换为24位RGB格式
                        using (var convertedBitmap = ConvertToFormat(bitmap, PixelFormat.Format24bppRgb))
                        {
                            return ExtractTrueColorData(convertedBitmap);
                        }
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("转换原始数据失败: " + ex.Message, ex);
            }
        }

        /// <summary>
        /// 保存为标准格式
        /// </summary>
        /// <param name="bitmap">位图对象</param>
        /// <param name="path">保存路径</param>
        /// <param name="format">图像格式</param>
        public static void SaveAsStandardFormat(Bitmap bitmap, string path, Enums.ImageFormat format)
        {
            if (bitmap == null)
            {
                throw new ArgumentNullException("bitmap");
            }

            if (string.IsNullOrEmpty(path))
            {
                throw new ArgumentNullException("path");
            }

            try
            {
                // 确保目录存在
                var directory = Path.GetDirectoryName(path);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                switch (format)
                {
                    case Enums.ImageFormat.PNG:
                        bitmap.Save(path, ImageFormat.Png);
                        break;
                    case Enums.ImageFormat.BMP:
                        bitmap.Save(path, ImageFormat.Bmp);
                        break;
                    case Enums.ImageFormat.JPG:
                        SaveAsJpeg(bitmap, path, Constants.DEFAULT_EXPORT_QUALITY);
                        break;
                    case Enums.ImageFormat.TGA:
                        SaveAsTga(bitmap, path);
                        break;
                    default:
                        throw new NotSupportedException("不支持的图像格式: " + format);
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("保存图像失败: " + ex.Message, ex);
            }
        }

        /// <summary>
        /// 加载标准格式图像
        /// </summary>
        /// <param name="path">图像路径</param>
        /// <returns>位图对象</returns>
        public static Bitmap LoadStandardFormat(string path)
        {
            if (string.IsNullOrEmpty(path))
            {
                throw new ArgumentNullException("path");
            }

            if (!File.Exists(path))
            {
                throw new FileNotFoundException("图像文件不存在: " + path);
            }

            try
            {
                var extension = Path.GetExtension(path).ToLowerInvariant();
                
                switch (extension)
                {
                    case ".tga":
                        return LoadTga(path);
                    default:
                        return new Bitmap(path);
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("加载图像失败: " + ex.Message, ex);
            }
        }

        /// <summary>
        /// 调整图像大小
        /// </summary>
        /// <param name="bitmap">原始位图</param>
        /// <param name="newWidth">新宽度</param>
        /// <param name="newHeight">新高度</param>
        /// <param name="scaleMode">缩放模式</param>
        /// <returns>调整后的位图</returns>
        public static Bitmap ResizeImage(Bitmap bitmap, int newWidth, int newHeight, Enums.ScaleMode scaleMode = Enums.ScaleMode.HighQualityBicubic)
        {
            if (bitmap == null)
            {
                throw new ArgumentNullException("bitmap");
            }

            if (newWidth <= 0 || newHeight <= 0)
            {
                throw new ArgumentException("新尺寸必须大于0");
            }

            var resizedBitmap = new Bitmap(newWidth, newHeight);
            
            using (var graphics = Graphics.FromImage(resizedBitmap))
            {
                // 设置插值模式
                switch (scaleMode)
                {
                    case Enums.ScaleMode.NearestNeighbor:
                        graphics.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.NearestNeighbor;
                        break;
                    case Enums.ScaleMode.Bilinear:
                        graphics.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.Bilinear;
                        break;
                    case Enums.ScaleMode.Bicubic:
                        graphics.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.Bicubic;
                        break;
                    case Enums.ScaleMode.HighQualityBicubic:
                        graphics.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
                        graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;
                        graphics.PixelOffsetMode = System.Drawing.Drawing2D.PixelOffsetMode.HighQuality;
                        graphics.CompositingQuality = System.Drawing.Drawing2D.CompositingQuality.HighQuality;
                        break;
                }

                graphics.DrawImage(bitmap, 0, 0, newWidth, newHeight);
            }

            return resizedBitmap;
        }

        #region 私有方法

        /// <summary>
        /// 创建索引色位图
        /// </summary>
        /// <param name="imageData">图像数据</param>
        /// <param name="width">宽度</param>
        /// <param name="height">高度</param>
        /// <param name="palette">调色板</param>
        /// <returns>位图对象</returns>
        private static Bitmap CreateIndexedBitmap(byte[] imageData, int width, int height, byte[] palette)
        {
            var bitmap = new Bitmap(width, height, PixelFormat.Format8bppIndexed);
            
            // 设置调色板
            var colorPalette = bitmap.Palette;
            var paletteSize = Math.Min(256, palette.Length / 3);
            
            for (int i = 0; i < paletteSize; i++)
            {
                var r = palette[i * 3];
                var g = palette[i * 3 + 1];
                var b = palette[i * 3 + 2];
                colorPalette.Entries[i] = Color.FromArgb(r, g, b);
            }
            
            // 设置透明色（索引0）
            if (paletteSize > 0)
            {
                colorPalette.Entries[0] = Color.FromArgb(0, colorPalette.Entries[0]);
            }
            
            bitmap.Palette = colorPalette;
            
            // 设置像素数据
            var bitmapData = bitmap.LockBits(
                new Rectangle(0, 0, width, height),
                ImageLockMode.WriteOnly,
                PixelFormat.Format8bppIndexed);
            
            try
            {
                var stride = bitmapData.Stride;
                var scan0 = bitmapData.Scan0;
                
                for (int y = 0; y < height; y++)
                {
                    var sourceOffset = y * width;
                    var destOffset = y * stride;
                    
                    if (sourceOffset + width <= imageData.Length)
                    {
                        Marshal.Copy(imageData, sourceOffset, IntPtr.Add(scan0, destOffset), Math.Min(width, stride));
                    }
                }
            }
            finally
            {
                bitmap.UnlockBits(bitmapData);
            }
            
            return bitmap;
        }

        /// <summary>
        /// 创建真彩色位图
        /// </summary>
        /// <param name="imageData">图像数据</param>
        /// <param name="width">宽度</param>
        /// <param name="height">高度</param>
        /// <returns>位图对象</returns>
        private static Bitmap CreateTrueColorBitmap(byte[] imageData, int width, int height)
        {
            var bitmap = new Bitmap(width, height, PixelFormat.Format24bppRgb);
            
            var bitmapData = bitmap.LockBits(
                new Rectangle(0, 0, width, height),
                ImageLockMode.WriteOnly,
                PixelFormat.Format24bppRgb);
            
            try
            {
                var stride = bitmapData.Stride;
                var scan0 = bitmapData.Scan0;
                
                for (int y = 0; y < height; y++)
                {
                    var sourceOffset = y * width * 3;
                    var destOffset = y * stride;
                    
                    if (sourceOffset + width * 3 <= imageData.Length)
                    {
                        Marshal.Copy(imageData, sourceOffset, IntPtr.Add(scan0, destOffset), Math.Min(width * 3, stride));
                    }
                }
            }
            finally
            {
                bitmap.UnlockBits(bitmapData);
            }
            
            return bitmap;
        }

        /// <summary>
        /// 提取索引色数据
        /// </summary>
        /// <param name="bitmap">位图对象</param>
        /// <param name="palette">输出调色板</param>
        /// <returns>图像数据</returns>
        private static byte[] ExtractIndexedData(Bitmap bitmap, out byte[] palette)
        {
            palette = new byte[Constants.PALETTE_BYTE_SIZE];
            
            // 提取调色板
            var colorPalette = bitmap.Palette;
            for (int i = 0; i < 256; i++)
            {
                var color = i < colorPalette.Entries.Length ? colorPalette.Entries[i] : Color.Black;
                palette[i * 3] = color.R;
                palette[i * 3 + 1] = color.G;
                palette[i * 3 + 2] = color.B;
            }
            
            // 提取像素数据
            var imageData = new byte[bitmap.Width * bitmap.Height];
            
            var bitmapData = bitmap.LockBits(
                new Rectangle(0, 0, bitmap.Width, bitmap.Height),
                ImageLockMode.ReadOnly,
                PixelFormat.Format8bppIndexed);
            
            try
            {
                var stride = bitmapData.Stride;
                var scan0 = bitmapData.Scan0;
                
                for (int y = 0; y < bitmap.Height; y++)
                {
                    var sourceOffset = y * stride;
                    var destOffset = y * bitmap.Width;
                    
                    Marshal.Copy(IntPtr.Add(scan0, sourceOffset), imageData, destOffset, bitmap.Width);
                }
            }
            finally
            {
                bitmap.UnlockBits(bitmapData);
            }
            
            return imageData;
        }

        /// <summary>
        /// 提取真彩色数据
        /// </summary>
        /// <param name="bitmap">位图对象</param>
        /// <returns>图像数据</returns>
        private static byte[] ExtractTrueColorData(Bitmap bitmap)
        {
            var imageData = new byte[bitmap.Width * bitmap.Height * 3];
            
            var bitmapData = bitmap.LockBits(
                new Rectangle(0, 0, bitmap.Width, bitmap.Height),
                ImageLockMode.ReadOnly,
                PixelFormat.Format24bppRgb);
            
            try
            {
                var stride = bitmapData.Stride;
                var scan0 = bitmapData.Scan0;
                
                for (int y = 0; y < bitmap.Height; y++)
                {
                    var sourceOffset = y * stride;
                    var destOffset = y * bitmap.Width * 3;
                    
                    Marshal.Copy(IntPtr.Add(scan0, sourceOffset), imageData, destOffset, bitmap.Width * 3);
                }
            }
            finally
            {
                bitmap.UnlockBits(bitmapData);
            }
            
            return imageData;
        }

        /// <summary>
        /// 转换位图格式
        /// </summary>
        /// <param name="bitmap">原始位图</param>
        /// <param name="format">目标格式</param>
        /// <returns>转换后的位图</returns>
        private static Bitmap ConvertToFormat(Bitmap bitmap, PixelFormat format)
        {
            var convertedBitmap = new Bitmap(bitmap.Width, bitmap.Height, format);
            
            using (var graphics = Graphics.FromImage(convertedBitmap))
            {
                graphics.DrawImage(bitmap, 0, 0);
            }
            
            return convertedBitmap;
        }

        /// <summary>
        /// 保存为JPEG格式
        /// </summary>
        /// <param name="bitmap">位图对象</param>
        /// <param name="path">保存路径</param>
        /// <param name="quality">质量（0-100）</param>
        private static void SaveAsJpeg(Bitmap bitmap, string path, int quality)
        {
            var encoder = GetEncoder(ImageFormat.Jpeg);
            var encoderParams = new EncoderParameters(1);
            encoderParams.Param[0] = new EncoderParameter(Encoder.Quality, quality);
            
            bitmap.Save(path, encoder, encoderParams);
        }

        /// <summary>
        /// 保存为TGA格式
        /// </summary>
        /// <param name="bitmap">位图对象</param>
        /// <param name="path">保存路径</param>
        private static void SaveAsTga(Bitmap bitmap, string path)
        {
            // TGA格式保存的简单实现
            // 这里只实现基本的24位RGB格式
            throw new NotImplementedException("TGA格式保存功能待实现");
        }

        /// <summary>
        /// 加载TGA格式图像
        /// </summary>
        /// <param name="path">图像路径</param>
        /// <returns>位图对象</returns>
        private static Bitmap LoadTga(string path)
        {
            // TGA格式加载的简单实现
            throw new NotImplementedException("TGA格式加载功能待实现");
        }

        /// <summary>
        /// 获取图像编码器
        /// </summary>
        /// <param name="format">图像格式</param>
        /// <returns>图像编码器</returns>
        private static ImageCodecInfo GetEncoder(ImageFormat format)
        {
            var codecs = ImageCodecInfo.GetImageDecoders();
            
            foreach (var codec in codecs)
            {
                if (codec.FormatID == format.Guid)
                {
                    return codec;
                }
            }
            
            return null;
        }

        #endregion
    }
}
