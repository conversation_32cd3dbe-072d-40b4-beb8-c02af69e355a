using System;
using System.Diagnostics;
using System.IO;
using System.Text;

class LoggedLauncher
{
    private static string logFile = "startup_log.txt";
    
    static void Main()
    {
        try
        {
            Log("=== 图档工具启动测试开始 ===");
            Log("时间: " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            
            var exePath = Path.Combine(Directory.GetCurrentDirectory(), @"GraphicTool.UI\bin\Debug\GraphicTool.UI.exe");
            Log("可执行文件路径: " + exePath);
            
            if (!File.Exists(exePath))
            {
                Log("错误: 找不到可执行文件");
                Console.WriteLine("错误: 找不到可执行文件 " + exePath);
                Console.ReadKey();
                return;
            }
            
            Log("文件存在，开始启动程序...");
            
            var startInfo = new ProcessStartInfo
            {
                FileName = exePath,
                UseShellExecute = false,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                CreateNoWindow = false,
                WindowStyle = ProcessWindowStyle.Normal
            };
            
            Log("进程启动参数配置完成");
            
            using (var process = new Process())
            {
                process.StartInfo = startInfo;
                
                // 设置事件处理器
                process.OutputDataReceived += (sender, e) => {
                    if (e.Data != null)
                    {
                        Log("标准输出: " + e.Data);
                    }
                };
                
                process.ErrorDataReceived += (sender, e) => {
                    if (e.Data != null)
                    {
                        Log("错误输出: " + e.Data);
                    }
                };
                
                process.Exited += (sender, e) => {
                    Log("进程退出事件触发，退出代码: " + process.ExitCode);
                };
                
                Log("开始启动进程...");
                bool started = process.Start();
                Log("进程启动结果: " + started);
                
                if (started)
                {
                    Log("进程ID: " + process.Id);
                    process.BeginOutputReadLine();
                    process.BeginErrorReadLine();
                    
                    Log("等待进程退出或超时...");
                    bool exited = process.WaitForExit(8000); // 等待8秒
                    
                    if (exited)
                    {
                        Log("进程已退出，退出代码: " + process.ExitCode);
                        Console.WriteLine("程序退出，退出代码: " + process.ExitCode);
                    }
                    else
                    {
                        Log("进程仍在运行，准备终止");
                        Console.WriteLine("程序仍在运行，正在终止...");
                        process.Kill();
                        Log("进程已被终止");
                    }
                }
                else
                {
                    Log("进程启动失败");
                    Console.WriteLine("进程启动失败");
                }
            }
        }
        catch (Exception ex)
        {
            Log("异常发生: " + ex.GetType().Name);
            Log("异常消息: " + ex.Message);
            Log("异常堆栈: " + ex.StackTrace);
            
            Console.WriteLine("启动失败: " + ex.Message);
            Console.WriteLine("详细信息已记录到日志文件: " + logFile);
        }
        
        Log("=== 测试结束 ===");
        Console.WriteLine("详细日志已保存到: " + logFile);
        Console.WriteLine("按任意键退出...");
        Console.ReadKey();
    }
    
    private static void Log(string message)
    {
        try
        {
            var logMessage = DateTime.Now.ToString("HH:mm:ss.fff") + " - " + message;
            File.AppendAllText(logFile, logMessage + Environment.NewLine, Encoding.UTF8);
            Console.WriteLine(logMessage);
        }
        catch
        {
            // 忽略日志写入错误
        }
    }
}
