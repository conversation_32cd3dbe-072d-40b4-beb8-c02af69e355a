using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;

namespace GraphicTool.Core.DataStructures
{
    /// <summary>
    /// 精灵数据结构
    /// 对应原始C++代码中的精灵数据结构
    /// 用于存储精灵的基本信息和动画列表
    /// </summary>
    [StructLayout(LayoutKind.Sequential, Pack = 1)]
    public struct SpriteData
    {
        /// <summary>
        /// 精灵编号
        /// </summary>
        public uint SprNo;

        /// <summary>
        /// 在文件中的偏移位置
        /// </summary>
        public uint Offset;

        /// <summary>
        /// 动画数量
        /// </summary>
        public uint AnimSize;

        /// <summary>
        /// 格式版本
        /// </summary>
        public byte Format;

        /// <summary>
        /// 保留字段1
        /// </summary>
        public byte Reserved1;

        /// <summary>
        /// 保留字段2
        /// </summary>
        public ushort Reserved2;

        /// <summary>
        /// 动画列表（运行时数据，不在文件结构中）
        /// </summary>
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 1)]
        private byte[] _animationsPlaceholder;

        /// <summary>
        /// 动画列表（实际使用的属性）
        /// </summary>
        public List<AnimList> Animations { get; set; }

        /// <summary>
        /// 检查数据是否有效
        /// </summary>
        public bool IsValid
        {
            get
            {
                return SprNo >= 0 && AnimSize > 0 && Offset > 0;
            }
        }

        /// <summary>
        /// 获取指定索引的动画
        /// </summary>
        /// <param name="index">动画索引</param>
        /// <returns>动画数据，如果索引无效返回null</returns>
        public AnimList? GetAnimation(int index)
        {
            if (Animations == null || index < 0 || index >= Animations.Count)
                return null;
            return Animations[index];
        }

        /// <summary>
        /// 添加动画
        /// </summary>
        /// <param name="animation">要添加的动画</param>
        public void AddAnimation(AnimList animation)
        {
            if (Animations == null)
                Animations = new List<AnimList>();
            
            Animations.Add(animation);
            AnimSize = (uint)Animations.Count;
        }

        /// <summary>
        /// 移除指定索引的动画
        /// </summary>
        /// <param name="index">动画索引</param>
        /// <returns>是否成功移除</returns>
        public bool RemoveAnimation(int index)
        {
            if (Animations == null || index < 0 || index >= Animations.Count)
                return false;

            Animations.RemoveAt(index);
            AnimSize = (uint)Animations.Count;
            return true;
        }

        /// <summary>
        /// 创建一个新的精灵数据
        /// </summary>
        /// <param name="sprNo">精灵编号</param>
        /// <param name="offset">文件偏移</param>
        /// <param name="format">格式版本</param>
        /// <returns>精灵数据结构</returns>
        public static SpriteData Create(uint sprNo, uint offset, byte format = 1)
        {
            return new SpriteData
            {
                SprNo = sprNo,
                Offset = offset,
                AnimSize = 0,
                Format = format,
                Reserved1 = 0,
                Reserved2 = 0,
                Animations = new List<AnimList>()
            };
        }

        /// <summary>
        /// 获取结构体的字节大小（不包括动画列表）
        /// </summary>
        public static int SizeOf { get { return 16; } } // 手动计算：4+4+4+1+1+2 = 16字节

        /// <summary>
        /// 获取总的内存使用大小（包括动画列表）
        /// </summary>
        public int GetTotalSize()
        {
            int size = SizeOf;
            if (Animations != null)
            {
                foreach (var anim in Animations)
                {
                    size += anim.GetTotalSize();
                }
            }
            return size;
        }

        public override string ToString()
        {
            return string.Format("SpriteData: No={0}, Offset=0x{1:X8}, AnimCount={2}, Format={3}",
                SprNo, Offset, AnimSize, Format);
        }
    }
}
