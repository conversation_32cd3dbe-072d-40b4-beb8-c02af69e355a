using System;
using System.Drawing;
using System.IO;
using System.Text;
using System.Windows.Forms;
using GraphicTool.Core.DataStructures;
using GraphicTool.Core.FileReaders;
using GraphicTool.Core.ImageProcessing;

namespace GraphicTool.UI
{
    /// <summary>
    /// 主窗口
    /// </summary>
    public partial class MainForm : Form
    {
        #region 私有字段

        /// <summary>
        /// 图档文件读取器
        /// </summary>
        private GraphicFileReader _graphicReader;

        /// <summary>
        /// 精灵文件读取器
        /// </summary>
        private SpriteFileReader _spriteReader;

        /// <summary>
        /// 当前打开的文件路径
        /// </summary>
        private string _currentPath;

        /// <summary>
        /// 状态栏标签
        /// </summary>
        private ToolStripStatusLabel _statusLabel;

        /// <summary>
        /// 进度条
        /// </summary>
        private ToolStripProgressBar _progressBar;

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public MainForm()
        {
            try
            {
                LogToFile("MainForm构造函数开始");
                InitializeComponent();
                LogToFile("InitializeComponent完成");
                InitializeCustomComponents();
                LogToFile("InitializeCustomComponents完成");
                SetupEventHandlers();
                LogToFile("SetupEventHandlers完成");
                LogToFile("MainForm构造函数结束");
            }
            catch (Exception ex)
            {
                LogToFile("MainForm构造函数异常: " + ex.GetType().Name);
                LogToFile("异常消息: " + ex.Message);
                LogToFile("异常堆栈: " + ex.StackTrace);
                throw; // 重新抛出异常
            }
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // 主窗口设置
            this.Text = "CrossGate 图档工具 v" + Constants.TOOL_VERSION;
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.MinimumSize = new Size(800, 600);
            this.Icon = CreateApplicationIcon();
            
            // 创建主菜单
            CreateMainMenu();
            
            // 创建工具栏
            CreateToolBar();
            
            // 创建状态栏
            CreateStatusBar();
            
            // 创建主要控件
            CreateMainControls();
            
            this.ResumeLayout(false);
            this.PerformLayout();
        }

        /// <summary>
        /// 初始化自定义组件
        /// </summary>
        private void InitializeCustomComponents()
        {
            _graphicReader = new GraphicFileReader();
            _spriteReader = new SpriteFileReader();
            _currentPath = string.Empty;
        }

        /// <summary>
        /// 设置事件处理器
        /// </summary>
        private void SetupEventHandlers()
        {
            this.Load += MainForm_Load;
            this.FormClosing += MainForm_FormClosing;
            this.Resize += MainForm_Resize;
        }

        #endregion

        #region 菜单创建

        /// <summary>
        /// 创建主菜单
        /// </summary>
        private void CreateMainMenu()
        {
            var menuStrip = new MenuStrip();
            
            // 文件菜单
            var fileMenu = new ToolStripMenuItem("文件(&F)");
            fileMenu.DropDownItems.Add(CreateMenuItem("打开图档文件夹(&O)", "Ctrl+O", OpenFolder_Click));
            fileMenu.DropDownItems.Add(new ToolStripSeparator());
            fileMenu.DropDownItems.Add(CreateMenuItem("导出当前图像(&E)", "Ctrl+E", ExportImage_Click));
            fileMenu.DropDownItems.Add(CreateMenuItem("批量导出(&B)", "Ctrl+Shift+E", BatchExport_Click));
            fileMenu.DropDownItems.Add(new ToolStripSeparator());
            fileMenu.DropDownItems.Add(CreateMenuItem("退出(&X)", "Alt+F4", Exit_Click));
            
            // 编辑菜单
            var editMenu = new ToolStripMenuItem("编辑(&E)");
            editMenu.DropDownItems.Add(CreateMenuItem("复制图像(&C)", "Ctrl+C", CopyImage_Click));
            editMenu.DropDownItems.Add(CreateMenuItem("保存图像(&S)", "Ctrl+S", SaveImage_Click));
            editMenu.DropDownItems.Add(new ToolStripSeparator());
            editMenu.DropDownItems.Add(CreateMenuItem("首选项(&P)", "Ctrl+,", Preferences_Click));
            
            // 视图菜单
            var viewMenu = new ToolStripMenuItem("视图(&V)");
            viewMenu.DropDownItems.Add(CreateMenuItem("刷新(&R)", "F5", Refresh_Click));
            viewMenu.DropDownItems.Add(new ToolStripSeparator());
            viewMenu.DropDownItems.Add(CreateMenuItem("缩放到适合(&F)", "Ctrl+0", ZoomToFit_Click));
            viewMenu.DropDownItems.Add(CreateMenuItem("实际大小(&A)", "Ctrl+1", ActualSize_Click));
            
            // 工具菜单
            var toolsMenu = new ToolStripMenuItem("工具(&T)");
            toolsMenu.DropDownItems.Add(CreateMenuItem("调色板编辑器(&P)", "Ctrl+P", PaletteEditor_Click));
            toolsMenu.DropDownItems.Add(CreateMenuItem("动画播放器(&A)", "Ctrl+A", AnimationPlayer_Click));
            toolsMenu.DropDownItems.Add(new ToolStripSeparator());
            toolsMenu.DropDownItems.Add(CreateMenuItem("文件验证(&V)", "Ctrl+V", ValidateFiles_Click));
            
            // 帮助菜单
            var helpMenu = new ToolStripMenuItem("帮助(&H)");
            helpMenu.DropDownItems.Add(CreateMenuItem("关于(&A)", "F1", About_Click));
            
            menuStrip.Items.AddRange(new ToolStripItem[] { fileMenu, editMenu, viewMenu, toolsMenu, helpMenu });
            this.MainMenuStrip = menuStrip;
            this.Controls.Add(menuStrip);
        }

        /// <summary>
        /// 创建菜单项
        /// </summary>
        /// <param name="text">文本</param>
        /// <param name="shortcut">快捷键</param>
        /// <param name="handler">事件处理器</param>
        /// <returns>菜单项</returns>
        private ToolStripMenuItem CreateMenuItem(string text, string shortcut, EventHandler handler)
        {
            var item = new ToolStripMenuItem(text);
            if (!string.IsNullOrEmpty(shortcut))
            {
                item.ShortcutKeyDisplayString = shortcut;
            }
            if (handler != null)
            {
                item.Click += handler;
            }
            return item;
        }

        #endregion

        #region 工具栏创建

        /// <summary>
        /// 创建工具栏
        /// </summary>
        private void CreateToolBar()
        {
            var toolStrip = new ToolStrip();
            toolStrip.ImageScalingSize = new Size(24, 24);
            
            // 添加工具栏按钮
            toolStrip.Items.Add(CreateToolButton("打开", "打开图档文件夹", OpenFolder_Click));
            toolStrip.Items.Add(new ToolStripSeparator());
            toolStrip.Items.Add(CreateToolButton("导出", "导出当前图像", ExportImage_Click));
            toolStrip.Items.Add(CreateToolButton("批量导出", "批量导出图像", BatchExport_Click));
            toolStrip.Items.Add(new ToolStripSeparator());
            toolStrip.Items.Add(CreateToolButton("刷新", "刷新文件列表", Refresh_Click));
            toolStrip.Items.Add(new ToolStripSeparator());
            toolStrip.Items.Add(CreateToolButton("缩放适合", "缩放到适合窗口", ZoomToFit_Click));
            toolStrip.Items.Add(CreateToolButton("实际大小", "显示实际大小", ActualSize_Click));
            
            this.Controls.Add(toolStrip);
        }

        /// <summary>
        /// 创建工具栏按钮
        /// </summary>
        /// <param name="text">文本</param>
        /// <param name="tooltip">提示文本</param>
        /// <param name="handler">事件处理器</param>
        /// <returns>工具栏按钮</returns>
        private ToolStripButton CreateToolButton(string text, string tooltip, EventHandler handler)
        {
            var button = new ToolStripButton(text);
            button.ToolTipText = tooltip;
            button.DisplayStyle = ToolStripItemDisplayStyle.ImageAndText;
            if (handler != null)
            {
                button.Click += handler;
            }
            return button;
        }

        #endregion

        #region 状态栏创建

        /// <summary>
        /// 创建状态栏
        /// </summary>
        private void CreateStatusBar()
        {
            var statusStrip = new StatusStrip();
            
            _statusLabel = new ToolStripStatusLabel("就绪");
            _statusLabel.Spring = true;
            _statusLabel.TextAlign = ContentAlignment.MiddleLeft;
            
            _progressBar = new ToolStripProgressBar();
            _progressBar.Visible = false;
            
            var versionLabel = new ToolStripStatusLabel("版本 " + Constants.TOOL_VERSION);
            
            statusStrip.Items.AddRange(new ToolStripItem[] { _statusLabel, _progressBar, versionLabel });
            this.Controls.Add(statusStrip);
        }

        #endregion

        #region 主要控件创建

        /// <summary>
        /// 创建主要控件
        /// </summary>
        private void CreateMainControls()
        {
            try
            {
                LogToFile("CreateMainControls开始");

                // 创建分割容器
                LogToFile("创建SplitContainer");
                var mainSplitter = new SplitContainer();
                mainSplitter.Dock = DockStyle.Fill;
                LogToFile("设置Dock属性");

                // 先设置最小尺寸，不设置SplitterDistance
                mainSplitter.Panel1MinSize = 200;
                mainSplitter.Panel2MinSize = 200; // 减小Panel2MinSize避免冲突
                LogToFile("设置Panel最小尺寸");

                // 延迟设置SplitterDistance，避免初始化时的错误
                LogToFile("准备设置SplitterDistance");
                this.Shown += (sender, e) => {
                    try
                    {
                        LogToFile("在Shown事件中设置SplitterDistance，窗口宽度: " + mainSplitter.Width);
                        if (mainSplitter.Width > 600)
                        {
                            var distance = Math.Min(300, mainSplitter.Width - mainSplitter.Panel2MinSize - 10);
                            distance = Math.Max(distance, mainSplitter.Panel1MinSize);
                            mainSplitter.SplitterDistance = distance;
                            LogToFile("SplitterDistance设置为: " + distance);
                        }
                        else
                        {
                            LogToFile("窗口太小，使用默认SplitterDistance");
                        }
                    }
                    catch (Exception ex)
                    {
                        LogToFile("设置SplitterDistance异常: " + ex.Message);
                    }
                };

                // 左侧面板 - 文件浏览器
                LogToFile("创建文件浏览器");
                CreateFileBrowser(mainSplitter.Panel1);
                LogToFile("文件浏览器创建完成");

                // 右侧面板 - 图像预览
                LogToFile("创建图像预览");
                CreateImagePreview(mainSplitter.Panel2);
                LogToFile("图像预览创建完成");

                LogToFile("添加SplitContainer到主窗口");
                this.Controls.Add(mainSplitter);
                LogToFile("CreateMainControls完成");
            }
            catch (Exception ex)
            {
                LogToFile("CreateMainControls异常: " + ex.GetType().Name);
                LogToFile("异常消息: " + ex.Message);
                LogToFile("异常堆栈: " + ex.StackTrace);
                throw;
            }
        }

        /// <summary>
        /// 创建文件浏览器
        /// </summary>
        /// <param name="parent">父容器</param>
        private void CreateFileBrowser(Control parent)
        {
            var groupBox = new GroupBox();
            groupBox.Text = "图档浏览器";
            groupBox.Dock = DockStyle.Fill;
            
            // 这里将在后续添加GraphicBrowser控件
            var placeholder = new Label();
            placeholder.Text = "图档浏览器\n(待实现)";
            placeholder.TextAlign = ContentAlignment.MiddleCenter;
            placeholder.Dock = DockStyle.Fill;
            
            groupBox.Controls.Add(placeholder);
            parent.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建图像预览
        /// </summary>
        /// <param name="parent">父容器</param>
        private void CreateImagePreview(Control parent)
        {
            var groupBox = new GroupBox();
            groupBox.Text = "图像预览";
            groupBox.Dock = DockStyle.Fill;
            
            // 这里将在后续添加ImagePreview控件
            var placeholder = new Label();
            placeholder.Text = "图像预览器\n(待实现)";
            placeholder.TextAlign = ContentAlignment.MiddleCenter;
            placeholder.Dock = DockStyle.Fill;
            
            groupBox.Controls.Add(placeholder);
            parent.Controls.Add(groupBox);
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 窗口加载事件
        /// </summary>
        private void MainForm_Load(object sender, EventArgs e)
        {
            UpdateStatus("CrossGate 图档工具已启动");
        }

        /// <summary>
        /// 窗口关闭事件
        /// </summary>
        private void MainForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            // 清理资源
            if (_graphicReader != null)
            {
                _graphicReader.Dispose();
            }
            
            if (_spriteReader != null)
            {
                _spriteReader.Dispose();
            }
        }

        /// <summary>
        /// 窗口大小改变事件
        /// </summary>
        private void MainForm_Resize(object sender, EventArgs e)
        {
            // 处理窗口大小改变
        }

        #endregion

        #region 菜单事件处理

        /// <summary>
        /// 打开文件夹
        /// </summary>
        private void OpenFolder_Click(object sender, EventArgs e)
        {
            using (var dialog = new FolderBrowserDialog())
            {
                dialog.Description = "选择包含图档文件的文件夹";
                dialog.ShowNewFolderButton = false;
                
                if (dialog.ShowDialog() == DialogResult.OK)
                {
                    LoadGraphicFiles(dialog.SelectedPath);
                }
            }
        }

        /// <summary>
        /// 导出图像
        /// </summary>
        private void ExportImage_Click(object sender, EventArgs e)
        {
            MessageBox.Show("导出图像功能待实现", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 批量导出
        /// </summary>
        private void BatchExport_Click(object sender, EventArgs e)
        {
            MessageBox.Show("批量导出功能待实现", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 退出程序
        /// </summary>
        private void Exit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        /// <summary>
        /// 复制图像
        /// </summary>
        private void CopyImage_Click(object sender, EventArgs e)
        {
            MessageBox.Show("复制图像功能待实现", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 保存图像
        /// </summary>
        private void SaveImage_Click(object sender, EventArgs e)
        {
            MessageBox.Show("保存图像功能待实现", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 首选项
        /// </summary>
        private void Preferences_Click(object sender, EventArgs e)
        {
            MessageBox.Show("首选项功能待实现", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 刷新
        /// </summary>
        private void Refresh_Click(object sender, EventArgs e)
        {
            if (!string.IsNullOrEmpty(_currentPath))
            {
                LoadGraphicFiles(_currentPath);
            }
        }

        /// <summary>
        /// 缩放到适合
        /// </summary>
        private void ZoomToFit_Click(object sender, EventArgs e)
        {
            MessageBox.Show("缩放到适合功能待实现", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 实际大小
        /// </summary>
        private void ActualSize_Click(object sender, EventArgs e)
        {
            MessageBox.Show("实际大小功能待实现", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 调色板编辑器
        /// </summary>
        private void PaletteEditor_Click(object sender, EventArgs e)
        {
            MessageBox.Show("调色板编辑器功能待实现", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 动画播放器
        /// </summary>
        private void AnimationPlayer_Click(object sender, EventArgs e)
        {
            MessageBox.Show("动画播放器功能待实现", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 文件验证
        /// </summary>
        private void ValidateFiles_Click(object sender, EventArgs e)
        {
            MessageBox.Show("文件验证功能待实现", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 关于
        /// </summary>
        private void About_Click(object sender, EventArgs e)
        {
            var aboutText = string.Format(
                "CrossGate 图档工具\n\n" +
                "版本: {0}\n" +
                "作者: Augment Agent\n" +
                "描述: CrossGate游戏图档文件的查看和编辑工具\n\n" +
                "支持的格式:\n" +
                "- RD压缩图像格式\n" +
                "- 多版本图档文件\n" +
                "- 精灵动画文件\n" +
                "- 标准图像格式导出",
                Constants.TOOL_VERSION);
                
            MessageBox.Show(aboutText, "关于", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 加载图档文件
        /// </summary>
        /// <param name="path">文件夹路径</param>
        private void LoadGraphicFiles(string path)
        {
            try
            {
                UpdateStatus("正在加载图档文件...");
                ShowProgress(true);
                
                _currentPath = path;
                
                // 尝试使用默认文件名初始化
                var success = _graphicReader.InitializeWithDefaults(path);
                
                if (success)
                {
                    UpdateStatus(string.Format("已加载 {0} 个图像", _graphicReader.LoadedImageCount));
                }
                else
                {
                    UpdateStatus("未找到有效的图档文件");
                    MessageBox.Show("在指定文件夹中未找到有效的图档文件", "警告", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                UpdateStatus("加载失败");
                MessageBox.Show("加载图档文件时发生错误:\n" + ex.Message, "错误", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                ShowProgress(false);
            }
        }

        /// <summary>
        /// 更新状态栏
        /// </summary>
        /// <param name="message">状态消息</param>
        private void UpdateStatus(string message)
        {
            if (_statusLabel != null)
            {
                _statusLabel.Text = message;
            }
        }

        /// <summary>
        /// 显示/隐藏进度条
        /// </summary>
        /// <param name="show">是否显示</param>
        private void ShowProgress(bool show)
        {
            if (_progressBar != null)
            {
                _progressBar.Visible = show;
            }
        }

        /// <summary>
        /// 创建应用程序图标
        /// </summary>
        /// <returns>图标</returns>
        private Icon CreateApplicationIcon()
        {
            // 创建简单的应用程序图标
            var bitmap = new Bitmap(32, 32);
            using (var g = Graphics.FromImage(bitmap))
            {
                g.Clear(Color.Blue);
                g.FillRectangle(Brushes.White, 8, 8, 16, 16);
                g.DrawString("CG", new Font("Arial", 8, FontStyle.Bold), Brushes.Blue, 10, 10);
            }
            
            return Icon.FromHandle(bitmap.GetHicon());
        }

        /// <summary>
        /// 记录日志到文件
        /// </summary>
        /// <param name="message">日志消息</param>
        private void LogToFile(string message)
        {
            try
            {
                var logMessage = DateTime.Now.ToString("HH:mm:ss.fff") + " - MainForm - " + message;
                File.AppendAllText("mainform_log.txt", logMessage + Environment.NewLine, Encoding.UTF8);
            }
            catch
            {
                // 忽略日志写入错误
            }
        }

        #endregion
    }
}
