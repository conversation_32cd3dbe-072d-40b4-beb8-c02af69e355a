using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;

namespace GraphicTool.Core.DataStructures
{
    /// <summary>
    /// 动画列表结构
    /// 对应原始C++代码中的ANIMLIST结构
    /// 用于存储单个动画的信息和帧列表
    /// </summary>
    [StructLayout(LayoutKind.Sequential, Pack = 1)]
    public struct AnimList
    {
        /// <summary>
        /// 动画方向
        /// </summary>
        public byte Direction;

        /// <summary>
        /// 动画编号
        /// </summary>
        public byte AnimNo;

        /// <summary>
        /// 帧数量
        /// </summary>
        public byte FrameCount;

        /// <summary>
        /// 动画播放模式
        /// </summary>
        public byte PlayMode;

        /// <summary>
        /// 动画总时长（毫秒）
        /// </summary>
        public uint TotalDuration;

        /// <summary>
        /// 循环次数（0表示无限循环）
        /// </summary>
        public ushort LoopCount;

        /// <summary>
        /// 保留字段
        /// </summary>
        public ushort Reserved;

        /// <summary>
        /// 帧列表（运行时数据，不在文件结构中）
        /// </summary>
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 1)]
        private byte[] _framesPlaceholder;

        /// <summary>
        /// 帧列表（实际使用的属性）
        /// </summary>
        public List<AnimFrame> Frames { get; set; }

        /// <summary>
        /// 检查数据是否有效
        /// </summary>
        public bool IsValid
        {
            get
            {
                return FrameCount > 0 && Direction >= 0 && Direction < 8;
            }
        }

        /// <summary>
        /// 获取动画播放模式枚举
        /// </summary>
        public Enums.AnimationPlayMode GetPlayMode()
        {
            if (PlayMode < (byte)Enums.AnimationPlayMode.Reverse)
                return (Enums.AnimationPlayMode)PlayMode;
            return Enums.AnimationPlayMode.Once;
        }

        /// <summary>
        /// 设置动画播放模式
        /// </summary>
        /// <param name="mode">播放模式</param>
        public void SetPlayMode(Enums.AnimationPlayMode mode)
        {
            PlayMode = (byte)mode;
        }

        /// <summary>
        /// 获取指定索引的帧
        /// </summary>
        /// <param name="index">帧索引</param>
        /// <returns>帧数据，如果索引无效返回null</returns>
        public AnimFrame? GetFrame(int index)
        {
            if (Frames == null || index < 0 || index >= Frames.Count)
                return null;
            return Frames[index];
        }

        /// <summary>
        /// 添加帧
        /// </summary>
        /// <param name="frame">要添加的帧</param>
        public void AddFrame(AnimFrame frame)
        {
            if (Frames == null)
                Frames = new List<AnimFrame>();
            
            Frames.Add(frame);
            FrameCount = (byte)Frames.Count;
            RecalculateTotalDuration();
        }

        /// <summary>
        /// 移除指定索引的帧
        /// </summary>
        /// <param name="index">帧索引</param>
        /// <returns>是否成功移除</returns>
        public bool RemoveFrame(int index)
        {
            if (Frames == null || index < 0 || index >= Frames.Count)
                return false;

            Frames.RemoveAt(index);
            FrameCount = (byte)Frames.Count;
            RecalculateTotalDuration();
            return true;
        }

        /// <summary>
        /// 重新计算总时长
        /// </summary>
        private void RecalculateTotalDuration()
        {
            TotalDuration = 0;
            if (Frames != null)
            {
                foreach (var frame in Frames)
                {
                    TotalDuration += frame.Duration;
                }
            }
        }

        /// <summary>
        /// 创建一个新的动画列表
        /// </summary>
        /// <param name="direction">方向</param>
        /// <param name="animNo">动画编号</param>
        /// <param name="playMode">播放模式</param>
        /// <param name="loopCount">循环次数</param>
        /// <returns>动画列表结构</returns>
        public static AnimList Create(byte direction, byte animNo, 
            Enums.AnimationPlayMode playMode = Enums.AnimationPlayMode.Loop, 
            ushort loopCount = 0)
        {
            return new AnimList
            {
                Direction = direction,
                AnimNo = animNo,
                FrameCount = 0,
                PlayMode = (byte)playMode,
                TotalDuration = 0,
                LoopCount = loopCount,
                Reserved = 0,
                Frames = new List<AnimFrame>()
            };
        }

        /// <summary>
        /// 获取结构体的字节大小（不包括帧列表）
        /// </summary>
        public static int SizeOf { get { return 16; } } // 手动计算：1+1+1+1+4+2+2+4 = 16字节

        /// <summary>
        /// 获取总的内存使用大小（包括帧列表）
        /// </summary>
        public int GetTotalSize()
        {
            int size = SizeOf;
            if (Frames != null)
            {
                size += Frames.Count * AnimFrame.SizeOf;
            }
            return size;
        }

        public override string ToString()
        {
            return string.Format("AnimList: Dir={0}, No={1}, Frames={2}, Mode={3}, Duration={4}ms",
                Direction, AnimNo, FrameCount, GetPlayMode(), TotalDuration);
        }
    }
}
