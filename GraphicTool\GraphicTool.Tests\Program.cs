using System;

namespace GraphicTool.Tests
{
    /// <summary>
    /// 测试程序主入口
    /// </summary>
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("CrossGate 图档工具 - 数据结构测试程序");
            Console.WriteLine("========================================");
            Console.WriteLine();

            try
            {
                // 运行数据结构测试
                DataStructureTests.RunAllTests();

                // 运行文件读取器测试
                FileReaderTests.RunAllTests();

                // 运行图像处理测试
                ImageProcessingTests.RunAllTests();

                Console.WriteLine("测试成功完成！");
            }
            catch (Exception ex)
            {
                Console.WriteLine("测试过程中发生错误: " + ex.Message);
                Console.WriteLine("详细信息: " + ex);
            }

            Console.WriteLine();
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
}
