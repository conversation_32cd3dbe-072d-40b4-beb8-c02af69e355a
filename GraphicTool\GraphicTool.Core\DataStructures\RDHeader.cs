using System;
using System.Runtime.InteropServices;

namespace GraphicTool.Core.DataStructures
{
    /// <summary>
    /// RD格式图像文件头部结构
    /// 对应原始C++代码中的RD_HEADER结构
    /// </summary>
    [StructLayout(LayoutKind.Sequential, Pack = 1)]
    public struct RDHeader
    {
        /// <summary>
        /// 文件标识符，应该是"RD"
        /// </summary>
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 2)]
        public byte[] Id;

        /// <summary>
        /// 压缩标志
        /// 0 = 未压缩
        /// 1 = 压缩
        /// 2 = 包含调色板
        /// 3 = 压缩且包含调色板
        /// </summary>
        public byte CompressFlag;

        /// <summary>
        /// 图像宽度（像素）
        /// </summary>
        public uint Width;

        /// <summary>
        /// 图像高度（像素）
        /// </summary>
        public uint Height;

        /// <summary>
        /// 数据大小（字节）
        /// </summary>
        public uint Size;

        /// <summary>
        /// 检查文件标识符是否有效
        /// </summary>
        public bool IsValidHeader
        {
            get
            {
                return Id != null && Id.Length == 2 && 
                       Id[0] == (byte)'R' && Id[1] == (byte)'D';
            }
        }

        /// <summary>
        /// 是否压缩
        /// </summary>
        public bool IsCompressed
        {
            get { return (CompressFlag & 1) != 0; }
        }

        /// <summary>
        /// 是否包含调色板
        /// </summary>
        public bool HasPalette
        {
            get { return (CompressFlag & 2) != 0; }
        }

        /// <summary>
        /// 创建一个新的RD头部
        /// </summary>
        /// <param name="width">图像宽度</param>
        /// <param name="height">图像高度</param>
        /// <param name="size">数据大小</param>
        /// <param name="compressed">是否压缩</param>
        /// <param name="hasPalette">是否包含调色板</param>
        /// <returns>RD头部结构</returns>
        public static RDHeader Create(uint width, uint height, uint size, bool compressed = false, bool hasPalette = false)
        {
            var header = new RDHeader
            {
                Id = new byte[] { (byte)'R', (byte)'D' },
                Width = width,
                Height = height,
                Size = size,
                CompressFlag = 0
            };

            if (compressed)
                header.CompressFlag |= 1;
            if (hasPalette)
                header.CompressFlag |= 2;

            return header;
        }

        /// <summary>
        /// 获取结构体的字节大小
        /// </summary>
        public static int SizeOf { get { return Marshal.SizeOf(typeof(RDHeader)); } }

        public override string ToString()
        {
            return string.Format("RDHeader: {0}x{1}, Size={2}, Compressed={3}, HasPalette={4}",
                Width, Height, Size, IsCompressed, HasPalette);
        }
    }
}
