using System;
using System.Runtime.InteropServices;

namespace GraphicTool.Core.DataStructures
{
    /// <summary>
    /// 动画帧结构
    /// 对应原始C++代码中的动画帧数据结构
    /// 用于存储单个动画帧的信息
    /// </summary>
    [StructLayout(LayoutKind.Sequential, Pack = 1)]
    public struct AnimFrame
    {
        /// <summary>
        /// 图像编号
        /// </summary>
        public uint GraphicNo;

        /// <summary>
        /// X轴偏移
        /// </summary>
        public short XOffset;

        /// <summary>
        /// Y轴偏移
        /// </summary>
        public short YOffset;

        /// <summary>
        /// 帧持续时间（毫秒）
        /// </summary>
        public uint Duration;

        /// <summary>
        /// 透明度（0-255）
        /// </summary>
        public byte Alpha;

        /// <summary>
        /// 混合模式
        /// </summary>
        public byte BlendMode;

        /// <summary>
        /// 缩放X（百分比，100表示原始大小）
        /// </summary>
        public byte ScaleX;

        /// <summary>
        /// 缩放Y（百分比，100表示原始大小）
        /// </summary>
        public byte ScaleY;

        /// <summary>
        /// 旋转角度（0-255对应0-360度）
        /// </summary>
        public byte Rotation;

        /// <summary>
        /// 特效标志
        /// </summary>
        public byte EffectFlags;

        /// <summary>
        /// 保留字段1
        /// </summary>
        public byte Reserved1;

        /// <summary>
        /// 保留字段2
        /// </summary>
        public byte Reserved2;

        /// <summary>
        /// 检查数据是否有效
        /// </summary>
        public bool IsValid
        {
            get
            {
                return Duration > 0 && Alpha <= 255 && ScaleX > 0 && ScaleY > 0;
            }
        }

        /// <summary>
        /// 是否可见
        /// </summary>
        public bool IsVisible
        {
            get { return Alpha > 0 && ScaleX > 0 && ScaleY > 0; }
        }

        /// <summary>
        /// 获取实际的旋转角度（度）
        /// </summary>
        public float GetRotationDegrees()
        {
            return (Rotation / 255.0f) * 360.0f;
        }

        /// <summary>
        /// 设置旋转角度（度）
        /// </summary>
        /// <param name="degrees">角度值（0-360）</param>
        public void SetRotationDegrees(float degrees)
        {
            // 确保角度在0-360范围内
            degrees = degrees % 360.0f;
            if (degrees < 0) degrees += 360.0f;
            
            Rotation = (byte)((degrees / 360.0f) * 255.0f);
        }

        /// <summary>
        /// 获取X缩放比例
        /// </summary>
        public float GetScaleXRatio()
        {
            return ScaleX / 100.0f;
        }

        /// <summary>
        /// 获取Y缩放比例
        /// </summary>
        public float GetScaleYRatio()
        {
            return ScaleY / 100.0f;
        }

        /// <summary>
        /// 设置缩放比例
        /// </summary>
        /// <param name="scaleX">X缩放比例</param>
        /// <param name="scaleY">Y缩放比例</param>
        public void SetScale(float scaleX, float scaleY)
        {
            ScaleX = (byte)Math.Max(1, Math.Min(255, scaleX * 100));
            ScaleY = (byte)Math.Max(1, Math.Min(255, scaleY * 100));
        }

        /// <summary>
        /// 检查是否有特效
        /// </summary>
        /// <param name="effectFlag">特效标志位</param>
        /// <returns>是否有指定特效</returns>
        public bool HasEffect(byte effectFlag)
        {
            return (EffectFlags & effectFlag) != 0;
        }

        /// <summary>
        /// 设置特效
        /// </summary>
        /// <param name="effectFlag">特效标志位</param>
        /// <param name="enabled">是否启用</param>
        public void SetEffect(byte effectFlag, bool enabled)
        {
            if (enabled)
                EffectFlags |= effectFlag;
            else
                EffectFlags = (byte)(EffectFlags & ~effectFlag);
        }

        /// <summary>
        /// 创建一个新的动画帧
        /// </summary>
        /// <param name="graphicNo">图像编号</param>
        /// <param name="duration">持续时间（毫秒）</param>
        /// <param name="xOffset">X偏移</param>
        /// <param name="yOffset">Y偏移</param>
        /// <param name="alpha">透明度</param>
        /// <returns>动画帧结构</returns>
        public static AnimFrame Create(uint graphicNo, uint duration = 100, 
            short xOffset = 0, short yOffset = 0, byte alpha = 255)
        {
            return new AnimFrame
            {
                GraphicNo = graphicNo,
                XOffset = xOffset,
                YOffset = yOffset,
                Duration = duration,
                Alpha = alpha,
                BlendMode = 0,
                ScaleX = 100,
                ScaleY = 100,
                Rotation = 0,
                EffectFlags = 0,
                Reserved1 = 0,
                Reserved2 = 0
            };
        }

        /// <summary>
        /// 获取结构体的字节大小
        /// </summary>
        public static int SizeOf { get { return Marshal.SizeOf(typeof(AnimFrame)); } }

        public override string ToString()
        {
            return string.Format("AnimFrame: Graphic={0}, Pos=({1},{2}), Duration={3}ms, Alpha={4}, Scale=({5}%,{6}%)",
                GraphicNo, XOffset, YOffset, Duration, Alpha, ScaleX, ScaleY);
        }
    }
}
