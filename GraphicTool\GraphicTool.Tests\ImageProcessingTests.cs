using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using GraphicTool.Core.DataStructures;
using GraphicTool.Core.ImageProcessing;

namespace GraphicTool.Tests
{
    /// <summary>
    /// 图像处理模块测试类
    /// </summary>
    public static class ImageProcessingTests
    {
        /// <summary>
        /// 运行所有图像处理测试
        /// </summary>
        public static void RunAllTests()
        {
            Console.WriteLine("=== 开始图像处理模块测试 ===");
            Console.WriteLine();

            TestImageConverter();
            TestPaletteManager();
            TestImageValidator();

            Console.WriteLine();
            Console.WriteLine("=== 图像处理模块测试完成 ===");
        }

        /// <summary>
        /// 测试图像转换器
        /// </summary>
        private static void TestImageConverter()
        {
            Console.WriteLine("测试 ImageConverter:");

            try
            {
                // 创建测试图像数据
                var testImageData = CreateTestImageData(32, 32);
                var testPalette = CreateTestPalette();

                // 测试转换为位图
                var bitmap = GraphicTool.Core.ImageProcessing.ImageConverter.ConvertToBitmap(testImageData, 32, 32, testPalette);
                Console.WriteLine("  转换为位图: " + bitmap.Width + "x" + bitmap.Height + ", 格式=" + bitmap.PixelFormat);

                // 测试转换为原始数据
                byte[] palette;
                var rawData = GraphicTool.Core.ImageProcessing.ImageConverter.ConvertToRawData(bitmap, out palette);
                Console.WriteLine("  转换为原始数据: " + rawData.Length + " 字节, 调色板=" + (palette != null ? palette.Length : 0) + " 字节");

                // 测试图像调整大小
                var resizedBitmap = GraphicTool.Core.ImageProcessing.ImageConverter.ResizeImage(bitmap, 64, 64, Enums.ScaleMode.HighQualityBicubic);
                Console.WriteLine("  调整大小: " + resizedBitmap.Width + "x" + resizedBitmap.Height);

                // 测试保存为标准格式
                var tempPath = Path.GetTempFileName() + ".png";
                try
                {
                    GraphicTool.Core.ImageProcessing.ImageConverter.SaveAsStandardFormat(bitmap, tempPath, Enums.ImageFormat.PNG);
                    Console.WriteLine("  保存PNG: 成功");

                    // 测试加载标准格式
                    var loadedBitmap = GraphicTool.Core.ImageProcessing.ImageConverter.LoadStandardFormat(tempPath);
                    Console.WriteLine("  加载PNG: " + loadedBitmap.Width + "x" + loadedBitmap.Height);
                    loadedBitmap.Dispose();
                }
                catch (Exception ex)
                {
                    Console.WriteLine("  保存/加载测试失败: " + ex.Message);
                }
                finally
                {
                    if (File.Exists(tempPath))
                    {
                        File.Delete(tempPath);
                    }
                }

                bitmap.Dispose();
                resizedBitmap.Dispose();
            }
            catch (Exception ex)
            {
                Console.WriteLine("  测试失败: " + ex.Message);
            }

            Console.WriteLine();
        }

        /// <summary>
        /// 测试调色板管理器
        /// </summary>
        private static void TestPaletteManager()
        {
            Console.WriteLine("测试 PaletteManager:");

            try
            {
                // 测试提取调色板
                var testData = new byte[Constants.PALETTE_BYTE_SIZE + 1024];
                for (int i = 0; i < Constants.PALETTE_BYTE_SIZE; i++)
                {
                    testData[i] = (byte)(i % 256);
                }

                var extractedPalette = PaletteManager.ExtractPalette(testData);
                Console.WriteLine("  提取调色板: " + extractedPalette.Length + " 字节");

                // 测试验证调色板
                var isValid = PaletteManager.ValidatePalette(extractedPalette);
                Console.WriteLine("  验证调色板: " + isValid);

                // 测试创建灰度调色板
                var grayscalePalette = PaletteManager.CreateGrayscalePalette();
                Console.WriteLine("  创建灰度调色板: " + grayscalePalette.Length + " 字节");

                // 测试创建彩虹调色板
                var rainbowPalette = PaletteManager.CreateRainbowPalette();
                Console.WriteLine("  创建彩虹调色板: " + rainbowPalette.Length + " 字节");

                // 测试应用调色板
                var indexData = new byte[32 * 32];
                for (int i = 0; i < indexData.Length; i++)
                {
                    indexData[i] = (byte)(i % 256);
                }

                var bitmap = PaletteManager.ApplyPalette(indexData, grayscalePalette, 32, 32);
                Console.WriteLine("  应用调色板: " + bitmap.Width + "x" + bitmap.Height);

                // 测试生成优化调色板
                var optimizedPalette = PaletteManager.GenerateOptimalPalette(bitmap);
                Console.WriteLine("  生成优化调色板: " + optimizedPalette.Length + " 字节");

                // 测试量化图像
                byte[] quantizedPalette;
                var quantizedData = PaletteManager.QuantizeImage(bitmap, out quantizedPalette);
                Console.WriteLine("  量化图像: " + quantizedData.Length + " 字节索引数据");

                // 测试混合调色板
                var blendedPalette = PaletteManager.BlendPalettes(grayscalePalette, rainbowPalette, 0.5);
                Console.WriteLine("  混合调色板: " + blendedPalette.Length + " 字节");

                bitmap.Dispose();
            }
            catch (Exception ex)
            {
                Console.WriteLine("  测试失败: " + ex.Message);
            }

            Console.WriteLine();
        }

        /// <summary>
        /// 测试图像验证器
        /// </summary>
        private static void TestImageValidator()
        {
            Console.WriteLine("测试 ImageValidator:");

            try
            {
                // 测试尺寸验证
                var validDimensions = ImageValidator.ValidateDimensions(64, 64);
                Console.WriteLine("  验证尺寸(64x64): " + validDimensions);

                var invalidDimensions = ImageValidator.ValidateDimensions(0, 64);
                Console.WriteLine("  验证尺寸(0x64): " + invalidDimensions);

                var oversizeDimensions = ImageValidator.ValidateDimensions(10000, 10000);
                Console.WriteLine("  验证尺寸(10000x10000): " + oversizeDimensions);

                // 测试调色板验证
                var validPalette = CreateTestPalette();
                var paletteValid = ImageValidator.ValidatePalette(validPalette);
                Console.WriteLine("  验证调色板(有效): " + paletteValid);

                var invalidPalette = new byte[100]; // 大小不正确
                var paletteInvalid = ImageValidator.ValidatePalette(invalidPalette);
                Console.WriteLine("  验证调色板(无效): " + paletteInvalid);

                // 测试RD数据验证
                var validRdData = CreateTestRDData();
                var rdValid = ImageValidator.ValidateRdData(validRdData);
                Console.WriteLine("  验证RD数据(有效): " + rdValid);

                var invalidRdData = new byte[10]; // 太小
                var rdInvalid = ImageValidator.ValidateRdData(invalidRdData);
                Console.WriteLine("  验证RD数据(无效): " + rdInvalid);

                // 测试位图验证
                using (var testBitmap = new Bitmap(64, 64, PixelFormat.Format24bppRgb))
                {
                    var bitmapValid = ImageValidator.ValidateImageIntegrity(testBitmap);
                    Console.WriteLine("  验证位图完整性: " + bitmapValid);

                    // 测试内存使用估算
                    var memoryUsage = ImageValidator.EstimateMemoryUsage(64, 64, PixelFormat.Format24bppRgb);
                    Console.WriteLine("  内存使用估算(64x64 RGB): " + memoryUsage + " 字节");
                }

                // 测试文件可读性检查
                var tempFile = Path.GetTempFileName();
                try
                {
                    File.WriteAllText(tempFile, "test");
                    var isReadable = ImageValidator.IsFileReadable(tempFile);
                    Console.WriteLine("  文件可读性检查: " + isReadable);

                    var isWritable = ImageValidator.IsFileWritable(tempFile);
                    Console.WriteLine("  文件可写性检查: " + isWritable);
                }
                finally
                {
                    if (File.Exists(tempFile))
                    {
                        File.Delete(tempFile);
                    }
                }

                // 测试不存在文件的检查
                var nonExistentFile = "non_existent_file.txt";
                var nonExistentReadable = ImageValidator.IsFileReadable(nonExistentFile);
                Console.WriteLine("  不存在文件可读性: " + nonExistentReadable);

                // 测试图像格式验证（会失败，因为没有实际图像文件）
                try
                {
                    var formatResult = ImageValidator.ValidateImageFormat("test.png");
                    Console.WriteLine("  图像格式验证: " + formatResult);
                }
                catch (Exception ex)
                {
                    Console.WriteLine("  图像格式验证（预期失败）: " + ex.Message);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("  测试失败: " + ex.Message);
            }

            Console.WriteLine();
        }

        #region 辅助方法

        /// <summary>
        /// 创建测试图像数据
        /// </summary>
        /// <param name="width">宽度</param>
        /// <param name="height">高度</param>
        /// <returns>测试图像数据</returns>
        private static byte[] CreateTestImageData(int width, int height)
        {
            var data = new byte[width * height];
            
            for (int y = 0; y < height; y++)
            {
                for (int x = 0; x < width; x++)
                {
                    // 创建渐变图案
                    var value = (byte)((x + y) % 256);
                    data[y * width + x] = value;
                }
            }
            
            return data;
        }

        /// <summary>
        /// 创建测试调色板
        /// </summary>
        /// <returns>测试调色板</returns>
        private static byte[] CreateTestPalette()
        {
            var palette = new byte[Constants.PALETTE_BYTE_SIZE];
            
            for (int i = 0; i < 256; i++)
            {
                palette[i * 3] = (byte)i;         // R
                palette[i * 3 + 1] = (byte)(255 - i); // G
                palette[i * 3 + 2] = (byte)(i / 2);   // B
            }
            
            return palette;
        }

        /// <summary>
        /// 创建测试RD数据
        /// </summary>
        /// <returns>测试RD数据</returns>
        private static byte[] CreateTestRDData()
        {
            var buffer = new byte[RDHeader.SizeOf + 100];
            int offset = 0;
            
            // RD标识
            buffer[offset++] = (byte)'R';
            buffer[offset++] = (byte)'D';
            
            // 压缩标志
            buffer[offset++] = Constants.COMPRESS_FLAG_NONE;
            
            // 宽度
            BitConverter.GetBytes((uint)32).CopyTo(buffer, offset);
            offset += 4;
            
            // 高度
            BitConverter.GetBytes((uint)32).CopyTo(buffer, offset);
            offset += 4;
            
            // 大小
            BitConverter.GetBytes((uint)100).CopyTo(buffer, offset);
            offset += 4;
            
            // 填充一些测试数据
            for (int i = offset; i < buffer.Length; i++)
            {
                buffer[i] = (byte)(i % 256);
            }
            
            return buffer;
        }

        #endregion
    }
}
