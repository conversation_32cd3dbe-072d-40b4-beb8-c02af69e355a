using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;
using System.Linq;
using GraphicTool.Core.DataStructures;

namespace GraphicTool.Core.ImageProcessing
{
    /// <summary>
    /// 调色板管理器
    /// 负责调色板的提取、应用和优化
    /// </summary>
    public static class PaletteManager
    {
        /// <summary>
        /// 从图像数据提取调色板
        /// </summary>
        /// <param name="imageData">图像数据</param>
        /// <returns>调色板数据</returns>
        public static byte[] ExtractPalette(byte[] imageData)
        {
            if (imageData == null)
            {
                throw new ArgumentNullException("imageData");
            }

            // 如果数据长度包含调色板，提取前768字节
            if (imageData.Length >= Constants.PALETTE_BYTE_SIZE)
            {
                var palette = new byte[Constants.PALETTE_BYTE_SIZE];
                Array.Copy(imageData, 0, palette, 0, Constants.PALETTE_BYTE_SIZE);
                return palette;
            }

            // 否则生成默认调色板
            return GenerateDefaultPalette();
        }

        /// <summary>
        /// 应用调色板到图像数据
        /// </summary>
        /// <param name="indexData">索引数据</param>
        /// <param name="palette">调色板</param>
        /// <returns>RGB图像数据</returns>
        public static Bitmap ApplyPalette(byte[] indexData, byte[] palette)
        {
            if (indexData == null)
            {
                throw new ArgumentNullException("indexData");
            }

            if (palette == null)
            {
                throw new ArgumentNullException("palette");
            }

            if (palette.Length < Constants.PALETTE_BYTE_SIZE)
            {
                throw new ArgumentException("调色板大小不足");
            }

            // 计算图像尺寸（假设为正方形）
            var size = (int)Math.Sqrt(indexData.Length);
            if (size * size != indexData.Length)
            {
                // 如果不是正方形，尝试常见的宽高比
                int width, height;
                size = FindBestDimensions(indexData.Length, out width, out height);
                return ApplyPalette(indexData, palette, width, height);
            }

            return ApplyPalette(indexData, palette, size, size);
        }

        /// <summary>
        /// 应用调色板到指定尺寸的图像数据
        /// </summary>
        /// <param name="indexData">索引数据</param>
        /// <param name="palette">调色板</param>
        /// <param name="width">宽度</param>
        /// <param name="height">高度</param>
        /// <returns>位图对象</returns>
        public static Bitmap ApplyPalette(byte[] indexData, byte[] palette, int width, int height)
        {
            if (indexData == null)
            {
                throw new ArgumentNullException("indexData");
            }

            if (palette == null)
            {
                throw new ArgumentNullException("palette");
            }

            if (width <= 0 || height <= 0)
            {
                throw new ArgumentException("图像尺寸必须大于0");
            }

            if (indexData.Length < width * height)
            {
                throw new ArgumentException("索引数据大小不足");
            }

            var bitmap = new Bitmap(width, height, PixelFormat.Format24bppRgb);

            var bitmapData = bitmap.LockBits(
                new Rectangle(0, 0, width, height),
                ImageLockMode.WriteOnly,
                PixelFormat.Format24bppRgb);

            try
            {
                var stride = bitmapData.Stride;
                var scan0 = bitmapData.Scan0;

                // 创建临时缓冲区
                var rowBuffer = new byte[stride];

                for (int y = 0; y < height; y++)
                {
                    // 清空行缓冲区
                    Array.Clear(rowBuffer, 0, rowBuffer.Length);

                    for (int x = 0; x < width; x++)
                    {
                        var index = indexData[y * width + x];
                        var paletteOffset = index * 3;

                        if (paletteOffset + 2 < palette.Length)
                        {
                            // BGR格式（位图标准）
                            rowBuffer[x * 3] = palette[paletteOffset + 2];     // B
                            rowBuffer[x * 3 + 1] = palette[paletteOffset + 1]; // G
                            rowBuffer[x * 3 + 2] = palette[paletteOffset];     // R
                        }
                    }

                    // 复制行数据到位图
                    System.Runtime.InteropServices.Marshal.Copy(
                        rowBuffer, 0,
                        IntPtr.Add(scan0, y * stride),
                        Math.Min(width * 3, stride));
                }
            }
            finally
            {
                bitmap.UnlockBits(bitmapData);
            }

            return bitmap;
        }

        /// <summary>
        /// 生成优化的调色板
        /// </summary>
        /// <param name="bitmap">位图对象</param>
        /// <returns>优化的调色板</returns>
        public static byte[] GenerateOptimalPalette(Bitmap bitmap)
        {
            if (bitmap == null)
            {
                throw new ArgumentNullException("bitmap");
            }

            // 收集所有颜色
            var colorCounts = new Dictionary<Color, int>();
            
            for (int y = 0; y < bitmap.Height; y++)
            {
                for (int x = 0; x < bitmap.Width; x++)
                {
                    var color = bitmap.GetPixel(x, y);
                    
                    if (colorCounts.ContainsKey(color))
                    {
                        colorCounts[color]++;
                    }
                    else
                    {
                        colorCounts[color] = 1;
                    }
                }
            }

            // 按使用频率排序，取前256种颜色
            var sortedColors = colorCounts
                .OrderByDescending(kvp => kvp.Value)
                .Take(256)
                .Select(kvp => kvp.Key)
                .ToList();

            // 生成调色板
            var palette = new byte[Constants.PALETTE_BYTE_SIZE];
            
            for (int i = 0; i < 256; i++)
            {
                Color color;
                
                if (i < sortedColors.Count)
                {
                    color = sortedColors[i];
                }
                else
                {
                    color = Color.Black; // 填充黑色
                }
                
                palette[i * 3] = color.R;
                palette[i * 3 + 1] = color.G;
                palette[i * 3 + 2] = color.B;
            }

            return palette;
        }

        /// <summary>
        /// 量化图像到256色
        /// </summary>
        /// <param name="bitmap">原始位图</param>
        /// <param name="palette">输出调色板</param>
        /// <returns>索引数据</returns>
        public static byte[] QuantizeImage(Bitmap bitmap, out byte[] palette)
        {
            if (bitmap == null)
            {
                throw new ArgumentNullException("bitmap");
            }

            // 生成优化调色板
            palette = GenerateOptimalPalette(bitmap);
            
            // 创建颜色到索引的映射
            var colorToIndex = new Dictionary<Color, byte>();
            
            for (int i = 0; i < 256; i++)
            {
                var r = palette[i * 3];
                var g = palette[i * 3 + 1];
                var b = palette[i * 3 + 2];
                var color = Color.FromArgb(r, g, b);
                
                if (!colorToIndex.ContainsKey(color))
                {
                    colorToIndex[color] = (byte)i;
                }
            }

            // 量化图像
            var indexData = new byte[bitmap.Width * bitmap.Height];
            
            for (int y = 0; y < bitmap.Height; y++)
            {
                for (int x = 0; x < bitmap.Width; x++)
                {
                    var color = bitmap.GetPixel(x, y);
                    var index = FindClosestColorIndex(color, palette);
                    indexData[y * bitmap.Width + x] = index;
                }
            }

            return indexData;
        }

        /// <summary>
        /// 创建灰度调色板
        /// </summary>
        /// <returns>灰度调色板</returns>
        public static byte[] CreateGrayscalePalette()
        {
            var palette = new byte[Constants.PALETTE_BYTE_SIZE];
            
            for (int i = 0; i < 256; i++)
            {
                palette[i * 3] = (byte)i;     // R
                palette[i * 3 + 1] = (byte)i; // G
                palette[i * 3 + 2] = (byte)i; // B
            }
            
            return palette;
        }

        /// <summary>
        /// 创建彩虹调色板
        /// </summary>
        /// <returns>彩虹调色板</returns>
        public static byte[] CreateRainbowPalette()
        {
            var palette = new byte[Constants.PALETTE_BYTE_SIZE];
            
            for (int i = 0; i < 256; i++)
            {
                var hue = (i / 255.0) * 360.0;
                var color = ColorFromHsv(hue, 1.0, 1.0);
                
                palette[i * 3] = color.R;
                palette[i * 3 + 1] = color.G;
                palette[i * 3 + 2] = color.B;
            }
            
            return palette;
        }

        /// <summary>
        /// 验证调色板
        /// </summary>
        /// <param name="palette">调色板数据</param>
        /// <returns>是否有效</returns>
        public static bool ValidatePalette(byte[] palette)
        {
            if (palette == null)
            {
                return false;
            }

            if (palette.Length != Constants.PALETTE_BYTE_SIZE)
            {
                return false;
            }

            // 检查是否有有效的颜色（不全是0）
            for (int i = 0; i < palette.Length; i++)
            {
                if (palette[i] != 0)
                {
                    return true;
                }
            }

            return false; // 全黑调色板可能无效
        }

        /// <summary>
        /// 混合两个调色板
        /// </summary>
        /// <param name="palette1">调色板1</param>
        /// <param name="palette2">调色板2</param>
        /// <param name="ratio">混合比例（0.0-1.0）</param>
        /// <returns>混合后的调色板</returns>
        public static byte[] BlendPalettes(byte[] palette1, byte[] palette2, double ratio)
        {
            if (palette1 == null || palette2 == null)
            {
                throw new ArgumentNullException("调色板不能为null");
            }

            if (palette1.Length != Constants.PALETTE_BYTE_SIZE || palette2.Length != Constants.PALETTE_BYTE_SIZE)
            {
                throw new ArgumentException("调色板大小不正确");
            }

            if (ratio < 0.0 || ratio > 1.0)
            {
                throw new ArgumentException("混合比例必须在0.0-1.0之间");
            }

            var blendedPalette = new byte[Constants.PALETTE_BYTE_SIZE];
            
            for (int i = 0; i < Constants.PALETTE_BYTE_SIZE; i++)
            {
                var value1 = palette1[i];
                var value2 = palette2[i];
                var blended = (byte)(value1 * (1.0 - ratio) + value2 * ratio);
                blendedPalette[i] = blended;
            }

            return blendedPalette;
        }

        #region 私有方法

        /// <summary>
        /// 生成默认调色板
        /// </summary>
        /// <returns>默认调色板</returns>
        private static byte[] GenerateDefaultPalette()
        {
            var palette = new byte[Constants.PALETTE_BYTE_SIZE];
            
            // 生成标准的256色调色板
            for (int i = 0; i < 256; i++)
            {
                if (i < 16)
                {
                    // 前16色：标准VGA颜色
                    var color = GetVgaColor(i);
                    palette[i * 3] = color.R;
                    palette[i * 3 + 1] = color.G;
                    palette[i * 3 + 2] = color.B;
                }
                else if (i < 232)
                {
                    // 216色立方体 (6x6x6)
                    var index = i - 16;
                    var r = (index / 36) * 51;
                    var g = ((index % 36) / 6) * 51;
                    var b = (index % 6) * 51;
                    
                    palette[i * 3] = (byte)r;
                    palette[i * 3 + 1] = (byte)g;
                    palette[i * 3 + 2] = (byte)b;
                }
                else
                {
                    // 24级灰度
                    var gray = (i - 232) * 10 + 8;
                    palette[i * 3] = (byte)gray;
                    palette[i * 3 + 1] = (byte)gray;
                    palette[i * 3 + 2] = (byte)gray;
                }
            }
            
            return palette;
        }

        /// <summary>
        /// 获取VGA标准颜色
        /// </summary>
        /// <param name="index">颜色索引</param>
        /// <returns>颜色</returns>
        private static Color GetVgaColor(int index)
        {
            var vgaColors = new Color[]
            {
                Color.Black, Color.DarkBlue, Color.DarkGreen, Color.DarkCyan,
                Color.DarkRed, Color.DarkMagenta, Color.Brown, Color.LightGray,
                Color.DarkGray, Color.Blue, Color.Green, Color.Cyan,
                Color.Red, Color.Magenta, Color.Yellow, Color.White
            };
            
            return index < vgaColors.Length ? vgaColors[index] : Color.Black;
        }

        /// <summary>
        /// 查找最接近的颜色索引
        /// </summary>
        /// <param name="color">目标颜色</param>
        /// <param name="palette">调色板</param>
        /// <returns>最接近的颜色索引</returns>
        private static byte FindClosestColorIndex(Color color, byte[] palette)
        {
            var minDistance = double.MaxValue;
            byte closestIndex = 0;
            
            for (int i = 0; i < 256; i++)
            {
                var r = palette[i * 3];
                var g = palette[i * 3 + 1];
                var b = palette[i * 3 + 2];
                
                // 计算欧几里得距离
                var distance = Math.Sqrt(
                    Math.Pow(color.R - r, 2) +
                    Math.Pow(color.G - g, 2) +
                    Math.Pow(color.B - b, 2));
                
                if (distance < minDistance)
                {
                    minDistance = distance;
                    closestIndex = (byte)i;
                }
            }
            
            return closestIndex;
        }

        /// <summary>
        /// 查找最佳图像尺寸
        /// </summary>
        /// <param name="dataLength">数据长度</param>
        /// <param name="width">输出宽度</param>
        /// <param name="height">输出高度</param>
        /// <returns>是否找到合适尺寸</returns>
        private static int FindBestDimensions(int dataLength, out int width, out int height)
        {
            // 尝试常见的宽高比
            var ratios = new[] { 1.0, 4.0/3.0, 16.0/9.0, 2.0, 3.0/2.0 };
            
            foreach (var ratio in ratios)
            {
                var w = (int)Math.Sqrt(dataLength * ratio);
                var h = dataLength / w;
                
                if (w * h == dataLength)
                {
                    width = w;
                    height = h;
                    return w;
                }
            }
            
            // 如果没有找到完美匹配，使用最接近的正方形
            var size = (int)Math.Sqrt(dataLength);
            width = size;
            height = dataLength / size;
            return size;
        }

        /// <summary>
        /// 从HSV创建颜色
        /// </summary>
        /// <param name="hue">色调</param>
        /// <param name="saturation">饱和度</param>
        /// <param name="value">明度</param>
        /// <returns>颜色</returns>
        private static Color ColorFromHsv(double hue, double saturation, double value)
        {
            var hi = Convert.ToInt32(Math.Floor(hue / 60)) % 6;
            var f = hue / 60 - Math.Floor(hue / 60);

            value = value * 255;
            var v = Convert.ToInt32(value);
            var p = Convert.ToInt32(value * (1 - saturation));
            var q = Convert.ToInt32(value * (1 - f * saturation));
            var t = Convert.ToInt32(value * (1 - (1 - f) * saturation));

            switch (hi)
            {
                case 0: return Color.FromArgb(255, v, t, p);
                case 1: return Color.FromArgb(255, q, v, p);
                case 2: return Color.FromArgb(255, p, v, t);
                case 3: return Color.FromArgb(255, p, q, v);
                case 4: return Color.FromArgb(255, t, p, v);
                default: return Color.FromArgb(255, v, p, q);
            }
        }

        #endregion
    }
}
