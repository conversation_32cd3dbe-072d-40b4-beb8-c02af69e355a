using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;
using GraphicTool.Core.DataStructures;
using GraphicTool.Core.FileReaders;
using GraphicTool.Core.ImageProcessing;

namespace GraphicTool.UI.Controls
{
    /// <summary>
    /// 图像预览器控件
    /// </summary>
    public partial class ImagePreview : UserControl
    {
        #region 私有字段

        /// <summary>
        /// 图像显示面板
        /// </summary>
        private Panel _imagePanel;

        /// <summary>
        /// 图像信息面板
        /// </summary>
        private Panel _infoPanel;

        /// <summary>
        /// 缩放滑块
        /// </summary>
        private TrackBar _zoomTrackBar;

        /// <summary>
        /// 缩放标签
        /// </summary>
        private Label _zoomLabel;

        /// <summary>
        /// 图像信息标签
        /// </summary>
        private Label _imageInfoLabel;

        /// <summary>
        /// 当前显示的位图
        /// </summary>
        private Bitmap _currentBitmap;

        /// <summary>
        /// 当前缩放比例
        /// </summary>
        private float _zoomFactor;

        /// <summary>
        /// 图档文件读取器
        /// </summary>
        private GraphicFileReader _graphicReader;

        /// <summary>
        /// 当前图像索引
        /// </summary>
        private int _currentImageIndex;

        /// <summary>
        /// 背景模式
        /// </summary>
        private BackgroundMode _backgroundMode;

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public ImagePreview()
        {
            InitializeComponent();
            _zoomFactor = 1.0f;
            _currentImageIndex = -1;
            _backgroundMode = BackgroundMode.Transparent;
        }

        #endregion

        #region 公共属性

        /// <summary>
        /// 图档文件读取器
        /// </summary>
        public GraphicFileReader GraphicReader
        {
            get { return _graphicReader; }
            set { _graphicReader = value; }
        }

        /// <summary>
        /// 当前缩放比例
        /// </summary>
        public float ZoomFactor
        {
            get { return _zoomFactor; }
            set
            {
                _zoomFactor = Math.Max(0.1f, Math.Min(10.0f, value));
                UpdateZoomDisplay();
                _imagePanel.Invalidate();
            }
        }

        /// <summary>
        /// 背景模式
        /// </summary>
        public BackgroundMode BackgroundMode
        {
            get { return _backgroundMode; }
            set
            {
                _backgroundMode = value;
                _imagePanel.Invalidate();
            }
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // 创建信息面板
            CreateInfoPanel();
            
            // 创建图像面板
            CreateImagePanel();
            
            this.ResumeLayout(false);
        }

        /// <summary>
        /// 创建信息面板
        /// </summary>
        private void CreateInfoPanel()
        {
            _infoPanel = new Panel();
            _infoPanel.Height = 80;
            _infoPanel.Dock = DockStyle.Bottom;
            _infoPanel.BackColor = SystemColors.Control;
            
            // 图像信息标签
            _imageInfoLabel = new Label();
            _imageInfoLabel.Location = new Point(10, 10);
            _imageInfoLabel.Size = new Size(300, 60);
            _imageInfoLabel.Text = "未选择图像";
            
            // 缩放标签
            _zoomLabel = new Label();
            _zoomLabel.Location = new Point(320, 10);
            _zoomLabel.Size = new Size(80, 20);
            _zoomLabel.Text = "缩放: 100%";
            
            // 缩放滑块
            _zoomTrackBar = new TrackBar();
            _zoomTrackBar.Location = new Point(320, 35);
            _zoomTrackBar.Size = new Size(200, 45);
            _zoomTrackBar.Minimum = 10;  // 10%
            _zoomTrackBar.Maximum = 1000; // 1000%
            _zoomTrackBar.Value = 100;    // 100%
            _zoomTrackBar.TickFrequency = 100;
            _zoomTrackBar.ValueChanged += ZoomTrackBar_ValueChanged;
            
            // 背景模式按钮
            var bgButton = new Button();
            bgButton.Location = new Point(530, 35);
            bgButton.Size = new Size(80, 25);
            bgButton.Text = "背景模式";
            bgButton.Click += BackgroundButton_Click;
            
            _infoPanel.Controls.AddRange(new Control[] { _imageInfoLabel, _zoomLabel, _zoomTrackBar, bgButton });
            this.Controls.Add(_infoPanel);
        }

        /// <summary>
        /// 创建图像面板
        /// </summary>
        private void CreateImagePanel()
        {
            _imagePanel = new Panel();
            _imagePanel.Dock = DockStyle.Fill;
            _imagePanel.BackColor = Color.White;
            _imagePanel.AutoScroll = true;
            _imagePanel.Paint += ImagePanel_Paint;
            _imagePanel.MouseWheel += ImagePanel_MouseWheel;
            
            this.Controls.Add(_imagePanel);
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 显示图像
        /// </summary>
        /// <param name="imageIndex">图像索引</param>
        public void ShowImage(int imageIndex)
        {
            if (_graphicReader == null || !_graphicReader.IsInitialized)
            {
                ClearImage();
                return;
            }

            try
            {
                _currentImageIndex = imageIndex;
                
                // 读取原始图像数据
                var rawData = _graphicReader.ReadRawImageData(imageIndex);
                
                // 解压缩图像
                var bitmap = _graphicReader.DecompressImage(rawData);
                
                // 设置当前位图
                SetCurrentBitmap(bitmap);
                
                // 更新图像信息
                UpdateImageInfo(imageIndex);
                
                // 刷新显示
                _imagePanel.Invalidate();
            }
            catch (Exception ex)
            {
                ShowError("加载图像失败: " + ex.Message);
            }
        }

        /// <summary>
        /// 清除图像
        /// </summary>
        public void ClearImage()
        {
            SetCurrentBitmap(null);
            _currentImageIndex = -1;
            _imageInfoLabel.Text = "未选择图像";
            _imagePanel.Invalidate();
        }

        /// <summary>
        /// 缩放到适合窗口
        /// </summary>
        public void ZoomToFit()
        {
            if (_currentBitmap == null)
                return;
            
            var panelSize = _imagePanel.ClientSize;
            var imageSize = _currentBitmap.Size;
            
            var scaleX = (float)panelSize.Width / imageSize.Width;
            var scaleY = (float)panelSize.Height / imageSize.Height;
            
            ZoomFactor = Math.Min(scaleX, scaleY);
        }

        /// <summary>
        /// 设置为实际大小
        /// </summary>
        public void ActualSize()
        {
            ZoomFactor = 1.0f;
        }

        /// <summary>
        /// 导出当前图像
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="format">图像格式</param>
        public void ExportImage(string filePath, Enums.ImageFormat format)
        {
            if (_currentBitmap == null)
            {
                throw new InvalidOperationException("没有可导出的图像");
            }

            GraphicTool.Core.ImageProcessing.ImageConverter.SaveAsStandardFormat(_currentBitmap, filePath, format);
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 设置当前位图
        /// </summary>
        /// <param name="bitmap">位图对象</param>
        private void SetCurrentBitmap(Bitmap bitmap)
        {
            if (_currentBitmap != null)
            {
                _currentBitmap.Dispose();
            }
            
            _currentBitmap = bitmap;
        }

        /// <summary>
        /// 更新图像信息
        /// </summary>
        /// <param name="imageIndex">图像索引</param>
        private void UpdateImageInfo(int imageIndex)
        {
            if (_graphicReader == null || _currentBitmap == null)
                return;
            
            try
            {
                var imageInfo = _graphicReader.GetImageInfo(imageIndex);
                
                var info = string.Format(
                    "图像 #{0}\n" +
                    "尺寸: {1}x{2}\n" +
                    "大小: {3} 字节\n" +
                    "格式: {4}",
                    imageIndex,
                    _currentBitmap.Width,
                    _currentBitmap.Height,
                    imageInfo.Size,
                    _currentBitmap.PixelFormat);
                
                _imageInfoLabel.Text = info;
            }
            catch (Exception)
            {
                _imageInfoLabel.Text = string.Format("图像 #{0}\n信息获取失败", imageIndex);
            }
        }

        /// <summary>
        /// 更新缩放显示
        /// </summary>
        private void UpdateZoomDisplay()
        {
            _zoomLabel.Text = string.Format("缩放: {0:F0}%", _zoomFactor * 100);
            _zoomTrackBar.Value = (int)(_zoomFactor * 100);
        }

        /// <summary>
        /// 显示错误信息
        /// </summary>
        /// <param name="message">错误消息</param>
        private void ShowError(string message)
        {
            _imageInfoLabel.Text = "错误: " + message;
            _imageInfoLabel.ForeColor = Color.Red;
        }

        /// <summary>
        /// 绘制透明背景
        /// </summary>
        /// <param name="g">图形对象</param>
        /// <param name="rect">绘制区域</param>
        private void DrawTransparentBackground(Graphics g, Rectangle rect)
        {
            const int checkSize = 16;
            
            using (var brush1 = new SolidBrush(Color.White))
            using (var brush2 = new SolidBrush(Color.LightGray))
            {
                for (int x = rect.X; x < rect.Right; x += checkSize)
                {
                    for (int y = rect.Y; y < rect.Bottom; y += checkSize)
                    {
                        var checkRect = new Rectangle(x, y, 
                            Math.Min(checkSize, rect.Right - x),
                            Math.Min(checkSize, rect.Bottom - y));
                        
                        var brush = ((x / checkSize) + (y / checkSize)) % 2 == 0 ? brush1 : brush2;
                        g.FillRectangle(brush, checkRect);
                    }
                }
            }
        }

        /// <summary>
        /// 获取背景颜色
        /// </summary>
        /// <returns>背景颜色</returns>
        private Color GetBackgroundColor()
        {
            switch (_backgroundMode)
            {
                case BackgroundMode.White: return Color.White;
                case BackgroundMode.Black: return Color.Black;
                case BackgroundMode.Gray: return Color.Gray;
                default: return Color.Transparent;
            }
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 图像面板绘制事件
        /// </summary>
        private void ImagePanel_Paint(object sender, PaintEventArgs e)
        {
            var g = e.Graphics;
            g.InterpolationMode = InterpolationMode.NearestNeighbor;
            g.PixelOffsetMode = PixelOffsetMode.Half;
            
            if (_currentBitmap == null)
            {
                // 绘制占位符
                var text = "未选择图像";
                var font = new Font("Microsoft YaHei", 12);
                var textSize = g.MeasureString(text, font);
                var textPos = new PointF(
                    (_imagePanel.Width - textSize.Width) / 2,
                    (_imagePanel.Height - textSize.Height) / 2);
                
                g.DrawString(text, font, Brushes.Gray, textPos);
                return;
            }
            
            // 计算图像绘制位置和大小
            var imageSize = new Size(
                (int)(_currentBitmap.Width * _zoomFactor),
                (int)(_currentBitmap.Height * _zoomFactor));
            
            var imagePos = new Point(
                Math.Max(0, (_imagePanel.Width - imageSize.Width) / 2),
                Math.Max(0, (_imagePanel.Height - imageSize.Height) / 2));
            
            var imageRect = new Rectangle(imagePos, imageSize);
            
            // 绘制背景
            if (_backgroundMode == BackgroundMode.Transparent)
            {
                DrawTransparentBackground(g, imageRect);
            }
            else
            {
                using (var brush = new SolidBrush(GetBackgroundColor()))
                {
                    g.FillRectangle(brush, imageRect);
                }
            }
            
            // 绘制图像
            g.DrawImage(_currentBitmap, imageRect);
            
            // 绘制边框
            using (var pen = new Pen(Color.DarkGray))
            {
                g.DrawRectangle(pen, imageRect);
            }
        }

        /// <summary>
        /// 缩放滑块值改变事件
        /// </summary>
        private void ZoomTrackBar_ValueChanged(object sender, EventArgs e)
        {
            ZoomFactor = _zoomTrackBar.Value / 100.0f;
        }

        /// <summary>
        /// 图像面板鼠标滚轮事件
        /// </summary>
        private void ImagePanel_MouseWheel(object sender, MouseEventArgs e)
        {
            if (ModifierKeys == Keys.Control)
            {
                var delta = e.Delta > 0 ? 1.1f : 0.9f;
                ZoomFactor *= delta;
            }
        }

        /// <summary>
        /// 背景按钮点击事件
        /// </summary>
        private void BackgroundButton_Click(object sender, EventArgs e)
        {
            // 循环切换背景模式
            var modes = Enum.GetValues(typeof(BackgroundMode));
            var currentIndex = Array.IndexOf(modes, _backgroundMode);
            var nextIndex = (currentIndex + 1) % modes.Length;
            BackgroundMode = (BackgroundMode)modes.GetValue(nextIndex);
        }

        #endregion

        #region 资源清理

        /// <summary>
        /// 释放资源
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                if (_currentBitmap != null)
                {
                    _currentBitmap.Dispose();
                    _currentBitmap = null;
                }
            }
            
            base.Dispose(disposing);
        }

        #endregion
    }

    #region 枚举

    /// <summary>
    /// 背景模式
    /// </summary>
    public enum BackgroundMode
    {
        Transparent,
        White,
        Black,
        Gray
    }

    #endregion
}
