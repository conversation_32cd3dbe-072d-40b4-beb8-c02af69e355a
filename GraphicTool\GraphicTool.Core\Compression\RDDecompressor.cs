using System;
using System.IO;
using GraphicTool.Core.DataStructures;

namespace GraphicTool.Core.Compression
{
    /// <summary>
    /// RD格式解压缩器
    /// 负责解压缩RD格式的图像数据
    /// </summary>
    public static class RDDecompressor
    {
        /// <summary>
        /// 解压缩RD格式数据
        /// </summary>
        /// <param name="compressedData">压缩数据</param>
        /// <returns>解压缩后的数据</returns>
        public static byte[] Decompress(byte[] compressedData)
        {
            if (compressedData == null || compressedData.Length < RDHeader.SizeOf)
            {
                throw new ArgumentException("压缩数据无效或太小");
            }

            // 解析RD头部
            var header = ParseRDHeader(compressedData);
            
            if (!header.IsValidHeader)
            {
                throw new InvalidDataException("无效的RD文件头部");
            }

            // 根据压缩标志选择解压方法
            if (header.IsCompressed)
            {
                return DecompressData(compressedData, header);
            }
            else
            {
                return ExtractUncompressedData(compressedData, header);
            }
        }

        /// <summary>
        /// 解析RD文件头部
        /// </summary>
        /// <param name="data">数据</param>
        /// <returns>RD头部结构</returns>
        private static RDHeader ParseRDHeader(byte[] data)
        {
            var header = new RDHeader();
            
            // 读取标识符
            header.Id = new byte[2];
            header.Id[0] = data[0];
            header.Id[1] = data[1];
            
            // 读取压缩标志
            header.CompressFlag = data[2];
            
            // 读取尺寸信息
            header.Width = BitConverter.ToUInt32(data, 3);
            header.Height = BitConverter.ToUInt32(data, 7);
            header.Size = BitConverter.ToUInt32(data, 11);
            
            return header;
        }

        /// <summary>
        /// 解压缩数据
        /// </summary>
        /// <param name="compressedData">压缩数据</param>
        /// <param name="header">RD头部</param>
        /// <returns>解压缩后的数据</returns>
        private static byte[] DecompressData(byte[] compressedData, RDHeader header)
        {
            var dataOffset = RDHeader.SizeOf;
            var paletteSize = header.HasPalette ? Constants.PALETTE_BYTE_SIZE : 0;
            var imageDataOffset = dataOffset + paletteSize;
            
            // 计算输出缓冲区大小
            var outputSize = (int)(header.Width * header.Height);
            if (header.HasPalette)
            {
                outputSize += Constants.PALETTE_BYTE_SIZE; // 8位索引色
            }
            else
            {
                outputSize *= 3; // 24位RGB
            }
            
            var output = new byte[outputSize];
            var outputPos = 0;
            
            // 如果有调色板，先复制调色板
            if (header.HasPalette)
            {
                Array.Copy(compressedData, dataOffset, output, 0, Constants.PALETTE_BYTE_SIZE);
                outputPos = Constants.PALETTE_BYTE_SIZE;
            }
            
            // 解压缩图像数据
            var inputPos = imageDataOffset;
            var inputEnd = compressedData.Length;
            
            while (inputPos < inputEnd && outputPos < output.Length)
            {
                var controlByte = compressedData[inputPos++];
                
                if ((controlByte & Constants.BIT_CMP) != 0)
                {
                    // 压缩数据块
                    outputPos = ProcessCompressedBlock(compressedData, inputPos, output, outputPos, controlByte, ref inputPos);
                }
                else
                {
                    // 非压缩数据块
                    outputPos = ProcessUncompressedBlock(compressedData, inputPos, output, outputPos, controlByte, ref inputPos);
                }
            }
            
            return output;
        }

        /// <summary>
        /// 提取未压缩数据
        /// </summary>
        /// <param name="data">数据</param>
        /// <param name="header">RD头部</param>
        /// <returns>图像数据</returns>
        private static byte[] ExtractUncompressedData(byte[] data, RDHeader header)
        {
            var dataOffset = RDHeader.SizeOf;
            var dataSize = (int)header.Size;
            
            if (data.Length < dataOffset + dataSize)
            {
                throw new InvalidDataException("数据大小不足");
            }
            
            var output = new byte[dataSize];
            Array.Copy(data, dataOffset, output, 0, dataSize);
            
            return output;
        }

        /// <summary>
        /// 处理压缩数据块
        /// </summary>
        /// <param name="input">输入数据</param>
        /// <param name="inputPos">输入位置</param>
        /// <param name="output">输出缓冲区</param>
        /// <param name="outputPos">输出位置</param>
        /// <param name="controlByte">控制字节</param>
        /// <param name="newInputPos">新的输入位置</param>
        /// <returns>新的输出位置</returns>
        private static int ProcessCompressedBlock(byte[] input, int inputPos, byte[] output, int outputPos, 
            byte controlByte, ref int newInputPos)
        {
            // 获取重复次数
            var repeatCount = (controlByte & 0x7F) + 1;
            
            if (inputPos >= input.Length)
            {
                newInputPos = inputPos;
                return outputPos;
            }
            
            var repeatByte = input[inputPos++];
            
            // 写入重复数据
            for (int i = 0; i < repeatCount && outputPos < output.Length; i++)
            {
                output[outputPos++] = repeatByte;
            }
            
            newInputPos = inputPos;
            return outputPos;
        }

        /// <summary>
        /// 处理非压缩数据块
        /// </summary>
        /// <param name="input">输入数据</param>
        /// <param name="inputPos">输入位置</param>
        /// <param name="output">输出缓冲区</param>
        /// <param name="outputPos">输出位置</param>
        /// <param name="controlByte">控制字节</param>
        /// <param name="newInputPos">新的输入位置</param>
        /// <returns>新的输出位置</returns>
        private static int ProcessUncompressedBlock(byte[] input, int inputPos, byte[] output, int outputPos, 
            byte controlByte, ref int newInputPos)
        {
            // 获取数据长度
            var dataLength = controlByte + 1;
            
            // 复制数据
            for (int i = 0; i < dataLength && inputPos < input.Length && outputPos < output.Length; i++)
            {
                output[outputPos++] = input[inputPos++];
            }
            
            newInputPos = inputPos;
            return outputPos;
        }

        /// <summary>
        /// 创建位图数据
        /// </summary>
        /// <param name="imageData">图像数据</param>
        /// <param name="width">宽度</param>
        /// <param name="height">高度</param>
        /// <param name="palette">调色板（可选）</param>
        /// <returns>位图对象</returns>
        public static System.Drawing.Bitmap CreateBitmap(byte[] imageData, int width, int height, byte[] palette = null)
        {
            if (imageData == null)
            {
                throw new ArgumentNullException("imageData");
            }

            if (width <= 0 || height <= 0)
            {
                throw new ArgumentException("图像尺寸必须大于0");
            }

            System.Drawing.Bitmap bitmap;

            if (palette != null)
            {
                // 8位索引色位图
                bitmap = Create8BitBitmap(imageData, width, height, palette);
            }
            else
            {
                // 24位RGB位图
                bitmap = Create24BitBitmap(imageData, width, height);
            }

            return bitmap;
        }

        /// <summary>
        /// 创建8位索引色位图
        /// </summary>
        /// <param name="imageData">图像数据</param>
        /// <param name="width">宽度</param>
        /// <param name="height">高度</param>
        /// <param name="palette">调色板</param>
        /// <returns>位图对象</returns>
        private static System.Drawing.Bitmap Create8BitBitmap(byte[] imageData, int width, int height, byte[] palette)
        {
            var bitmap = new System.Drawing.Bitmap(width, height, System.Drawing.Imaging.PixelFormat.Format8bppIndexed);
            
            // 设置调色板
            var colorPalette = bitmap.Palette;
            for (int i = 0; i < 256 && i * 3 < palette.Length; i++)
            {
                var r = palette[i * 3];
                var g = palette[i * 3 + 1];
                var b = palette[i * 3 + 2];
                colorPalette.Entries[i] = System.Drawing.Color.FromArgb(r, g, b);
            }
            bitmap.Palette = colorPalette;
            
            // 设置像素数据
            var bitmapData = bitmap.LockBits(
                new System.Drawing.Rectangle(0, 0, width, height),
                System.Drawing.Imaging.ImageLockMode.WriteOnly,
                System.Drawing.Imaging.PixelFormat.Format8bppIndexed);
            
            try
            {
                var stride = bitmapData.Stride;
                var scan0 = bitmapData.Scan0;
                
                for (int y = 0; y < height; y++)
                {
                    var sourceOffset = y * width;
                    var destOffset = y * stride;
                    
                    if (sourceOffset + width <= imageData.Length)
                    {
                        System.Runtime.InteropServices.Marshal.Copy(
                            imageData, sourceOffset, 
                            IntPtr.Add(scan0, destOffset), 
                            Math.Min(width, stride));
                    }
                }
            }
            finally
            {
                bitmap.UnlockBits(bitmapData);
            }
            
            return bitmap;
        }

        /// <summary>
        /// 创建24位RGB位图
        /// </summary>
        /// <param name="imageData">图像数据</param>
        /// <param name="width">宽度</param>
        /// <param name="height">高度</param>
        /// <returns>位图对象</returns>
        private static System.Drawing.Bitmap Create24BitBitmap(byte[] imageData, int width, int height)
        {
            var bitmap = new System.Drawing.Bitmap(width, height, System.Drawing.Imaging.PixelFormat.Format24bppRgb);
            
            var bitmapData = bitmap.LockBits(
                new System.Drawing.Rectangle(0, 0, width, height),
                System.Drawing.Imaging.ImageLockMode.WriteOnly,
                System.Drawing.Imaging.PixelFormat.Format24bppRgb);
            
            try
            {
                var stride = bitmapData.Stride;
                var scan0 = bitmapData.Scan0;
                
                for (int y = 0; y < height; y++)
                {
                    var sourceOffset = y * width * 3;
                    var destOffset = y * stride;
                    
                    if (sourceOffset + width * 3 <= imageData.Length)
                    {
                        System.Runtime.InteropServices.Marshal.Copy(
                            imageData, sourceOffset, 
                            IntPtr.Add(scan0, destOffset), 
                            Math.Min(width * 3, stride));
                    }
                }
            }
            finally
            {
                bitmap.UnlockBits(bitmapData);
            }
            
            return bitmap;
        }
    }
}
