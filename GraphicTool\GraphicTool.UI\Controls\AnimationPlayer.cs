using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Windows.Forms;
using GraphicTool.Core.DataStructures;
using GraphicTool.Core.FileReaders;

namespace GraphicTool.UI.Controls
{
    /// <summary>
    /// 动画播放器控件
    /// </summary>
    public partial class AnimationPlayer : UserControl
    {
        #region 私有字段

        /// <summary>
        /// 动画显示面板
        /// </summary>
        private Panel _animationPanel;

        /// <summary>
        /// 控制面板
        /// </summary>
        private Panel _controlPanel;

        /// <summary>
        /// 播放/暂停按钮
        /// </summary>
        private Button _playButton;

        /// <summary>
        /// 停止按钮
        /// </summary>
        private Button _stopButton;

        /// <summary>
        /// 帧进度条
        /// </summary>
        private TrackBar _frameTrackBar;

        /// <summary>
        /// 帧信息标签
        /// </summary>
        private Label _frameInfoLabel;

        /// <summary>
        /// 速度控制
        /// </summary>
        private TrackBar _speedTrackBar;

        /// <summary>
        /// 动画定时器
        /// </summary>
        private Timer _animationTimer;

        /// <summary>
        /// 图档文件读取器
        /// </summary>
        private GraphicFileReader _graphicReader;

        /// <summary>
        /// 精灵文件读取器
        /// </summary>
        private SpriteFileReader _spriteReader;

        /// <summary>
        /// 当前动画列表
        /// </summary>
        private AnimList? _currentAnimation;

        /// <summary>
        /// 当前帧索引
        /// </summary>
        private int _currentFrameIndex;

        /// <summary>
        /// 是否正在播放
        /// </summary>
        private bool _isPlaying;

        /// <summary>
        /// 播放速度倍数
        /// </summary>
        private float _playbackSpeed;

        /// <summary>
        /// 缓存的帧位图
        /// </summary>
        private Dictionary<uint, Bitmap> _frameCache;

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public AnimationPlayer()
        {
            InitializeComponent();
            _currentFrameIndex = 0;
            _isPlaying = false;
            _playbackSpeed = 1.0f;
            _frameCache = new Dictionary<uint, Bitmap>();
        }

        #endregion

        #region 公共属性

        /// <summary>
        /// 图档文件读取器
        /// </summary>
        public GraphicFileReader GraphicReader
        {
            get { return _graphicReader; }
            set { _graphicReader = value; }
        }

        /// <summary>
        /// 精灵文件读取器
        /// </summary>
        public SpriteFileReader SpriteReader
        {
            get { return _spriteReader; }
            set { _spriteReader = value; }
        }

        /// <summary>
        /// 是否正在播放
        /// </summary>
        public bool IsPlaying
        {
            get { return _isPlaying; }
        }

        /// <summary>
        /// 播放速度倍数
        /// </summary>
        public float PlaybackSpeed
        {
            get { return _playbackSpeed; }
            set
            {
                _playbackSpeed = Math.Max(0.1f, Math.Min(5.0f, value));
                UpdateTimerInterval();
            }
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // 创建控制面板
            CreateControlPanel();
            
            // 创建动画面板
            CreateAnimationPanel();
            
            // 创建动画定时器
            CreateAnimationTimer();
            
            this.ResumeLayout(false);
        }

        /// <summary>
        /// 创建控制面板
        /// </summary>
        private void CreateControlPanel()
        {
            _controlPanel = new Panel();
            _controlPanel.Height = 80;
            _controlPanel.Dock = DockStyle.Bottom;
            _controlPanel.BackColor = SystemColors.Control;
            
            // 播放/暂停按钮
            _playButton = new Button();
            _playButton.Location = new Point(10, 10);
            _playButton.Size = new Size(60, 25);
            _playButton.Text = "播放";
            _playButton.Click += PlayButton_Click;
            
            // 停止按钮
            _stopButton = new Button();
            _stopButton.Location = new Point(80, 10);
            _stopButton.Size = new Size(60, 25);
            _stopButton.Text = "停止";
            _stopButton.Click += StopButton_Click;
            
            // 帧进度条
            _frameTrackBar = new TrackBar();
            _frameTrackBar.Location = new Point(150, 10);
            _frameTrackBar.Size = new Size(200, 45);
            _frameTrackBar.Minimum = 0;
            _frameTrackBar.Maximum = 0;
            _frameTrackBar.ValueChanged += FrameTrackBar_ValueChanged;
            
            // 帧信息标签
            _frameInfoLabel = new Label();
            _frameInfoLabel.Location = new Point(360, 15);
            _frameInfoLabel.Size = new Size(100, 20);
            _frameInfoLabel.Text = "帧: 0/0";
            
            // 速度标签
            var speedLabel = new Label();
            speedLabel.Location = new Point(10, 45);
            speedLabel.Size = new Size(40, 20);
            speedLabel.Text = "速度:";
            
            // 速度控制
            _speedTrackBar = new TrackBar();
            _speedTrackBar.Location = new Point(50, 40);
            _speedTrackBar.Size = new Size(150, 45);
            _speedTrackBar.Minimum = 10;  // 0.1x
            _speedTrackBar.Maximum = 500; // 5.0x
            _speedTrackBar.Value = 100;   // 1.0x
            _speedTrackBar.TickFrequency = 50;
            _speedTrackBar.ValueChanged += SpeedTrackBar_ValueChanged;
            
            // 速度显示标签
            var speedDisplayLabel = new Label();
            speedDisplayLabel.Location = new Point(210, 50);
            speedDisplayLabel.Size = new Size(50, 20);
            speedDisplayLabel.Text = "1.0x";
            speedDisplayLabel.Name = "SpeedDisplay";
            
            _controlPanel.Controls.AddRange(new Control[] 
            { 
                _playButton, _stopButton, _frameTrackBar, _frameInfoLabel,
                speedLabel, _speedTrackBar, speedDisplayLabel
            });
            
            this.Controls.Add(_controlPanel);
        }

        /// <summary>
        /// 创建动画面板
        /// </summary>
        private void CreateAnimationPanel()
        {
            _animationPanel = new Panel();
            _animationPanel.Dock = DockStyle.Fill;
            _animationPanel.BackColor = Color.White;
            _animationPanel.Paint += AnimationPanel_Paint;
            
            this.Controls.Add(_animationPanel);
        }

        /// <summary>
        /// 创建动画定时器
        /// </summary>
        private void CreateAnimationTimer()
        {
            _animationTimer = new Timer();
            _animationTimer.Interval = 100; // 默认100ms
            _animationTimer.Tick += AnimationTimer_Tick;
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 加载动画
        /// </summary>
        /// <param name="spriteIndex">精灵索引</param>
        /// <param name="animationIndex">动画索引</param>
        public void LoadAnimation(int spriteIndex, int animationIndex)
        {
            if (_spriteReader == null || !_spriteReader.IsInitialized)
            {
                ClearAnimation();
                return;
            }

            try
            {
                var spriteData = _spriteReader.GetSpriteData(spriteIndex);
                if (spriteData.HasValue)
                {
                    var animation = spriteData.Value.GetAnimation(animationIndex);
                    if (animation.HasValue)
                    {
                        LoadAnimation(animation.Value);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("加载动画失败: " + ex.Message, "错误", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 加载动画
        /// </summary>
        /// <param name="animation">动画数据</param>
        public void LoadAnimation(AnimList animation)
        {
            Stop();
            ClearFrameCache();
            
            _currentAnimation = animation;
            _currentFrameIndex = 0;
            
            // 更新控件状态
            _frameTrackBar.Maximum = Math.Max(0, animation.FrameCount - 1);
            _frameTrackBar.Value = 0;
            
            UpdateFrameInfo();
            PreloadFrames();
            
            _animationPanel.Invalidate();
        }

        /// <summary>
        /// 播放动画
        /// </summary>
        public void Play()
        {
            if (_currentAnimation.HasValue && _currentAnimation.Value.FrameCount > 0)
            {
                _isPlaying = true;
                _playButton.Text = "暂停";
                _animationTimer.Start();
            }
        }

        /// <summary>
        /// 暂停动画
        /// </summary>
        public void Pause()
        {
            _isPlaying = false;
            _playButton.Text = "播放";
            _animationTimer.Stop();
        }

        /// <summary>
        /// 停止动画
        /// </summary>
        public void Stop()
        {
            Pause();
            _currentFrameIndex = 0;
            _frameTrackBar.Value = 0;
            UpdateFrameInfo();
            _animationPanel.Invalidate();
        }

        /// <summary>
        /// 清除动画
        /// </summary>
        public void ClearAnimation()
        {
            Stop();
            _currentAnimation = null;
            _frameTrackBar.Maximum = 0;
            UpdateFrameInfo();
            ClearFrameCache();
            _animationPanel.Invalidate();
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 更新定时器间隔
        /// </summary>
        private void UpdateTimerInterval()
        {
            if (_currentAnimation.HasValue && _currentFrameIndex < _currentAnimation.Value.FrameCount)
            {
                var frame = _currentAnimation.Value.GetFrame(_currentFrameIndex);
                if (frame.HasValue)
                {
                    var interval = (int)(frame.Value.Duration / _playbackSpeed);
                    _animationTimer.Interval = Math.Max(10, interval);
                }
            }
        }

        /// <summary>
        /// 更新帧信息
        /// </summary>
        private void UpdateFrameInfo()
        {
            if (_currentAnimation.HasValue)
            {
                _frameInfoLabel.Text = string.Format("帧: {0}/{1}", 
                    _currentFrameIndex + 1, _currentAnimation.Value.FrameCount);
            }
            else
            {
                _frameInfoLabel.Text = "帧: 0/0";
            }
        }

        /// <summary>
        /// 预加载帧
        /// </summary>
        private void PreloadFrames()
        {
            if (_currentAnimation.HasValue && _graphicReader != null)
            {
                var animation = _currentAnimation.Value;
                
                for (int i = 0; i < animation.FrameCount; i++)
                {
                    var frame = animation.GetFrame(i);
                    if (frame.HasValue)
                    {
                        LoadFrameBitmap(frame.Value);
                    }
                }
            }
        }

        /// <summary>
        /// 加载帧位图
        /// </summary>
        /// <param name="frame">动画帧</param>
        /// <returns>位图对象</returns>
        private Bitmap LoadFrameBitmap(AnimFrame frame)
        {
            if (_frameCache.ContainsKey(frame.GraphicNo))
            {
                return _frameCache[frame.GraphicNo];
            }

            try
            {
                if (_graphicReader != null && _graphicReader.ImageExists((int)frame.GraphicNo))
                {
                    var rawData = _graphicReader.ReadRawImageData((int)frame.GraphicNo);
                    var bitmap = _graphicReader.DecompressImage(rawData);
                    
                    _frameCache[frame.GraphicNo] = bitmap;
                    return bitmap;
                }
            }
            catch (Exception)
            {
                // 忽略加载失败的帧
            }

            return null;
        }

        /// <summary>
        /// 清除帧缓存
        /// </summary>
        private void ClearFrameCache()
        {
            foreach (var bitmap in _frameCache.Values)
            {
                if (bitmap != null)
                    bitmap.Dispose();
            }
            _frameCache.Clear();
        }

        /// <summary>
        /// 绘制当前帧
        /// </summary>
        /// <param name="g">图形对象</param>
        private void DrawCurrentFrame(Graphics g)
        {
            if (!_currentAnimation.HasValue || _currentFrameIndex >= _currentAnimation.Value.FrameCount)
                return;

            var frame = _currentAnimation.Value.GetFrame(_currentFrameIndex);
            if (!frame.HasValue)
                return;

            var frameData = frame.Value;
            var bitmap = LoadFrameBitmap(frameData);
            
            if (bitmap == null)
                return;

            // 计算绘制位置
            var panelCenter = new Point(_animationPanel.Width / 2, _animationPanel.Height / 2);
            var drawPos = new Point(
                panelCenter.X + frameData.XOffset - bitmap.Width / 2,
                panelCenter.Y + frameData.YOffset - bitmap.Height / 2);

            // 应用变换
            var oldTransform = g.Transform;
            
            try
            {
                // 应用缩放
                if (frameData.ScaleX != 100 || frameData.ScaleY != 100)
                {
                    var scaleX = frameData.ScaleX / 100.0f;
                    var scaleY = frameData.ScaleY / 100.0f;
                    g.ScaleTransform(scaleX, scaleY);
                    drawPos.X = (int)(drawPos.X / scaleX);
                    drawPos.Y = (int)(drawPos.Y / scaleY);
                }

                // 应用旋转
                if (frameData.Rotation != 0)
                {
                    g.TranslateTransform(panelCenter.X, panelCenter.Y);
                    g.RotateTransform(frameData.Rotation);
                    g.TranslateTransform(-panelCenter.X, -panelCenter.Y);
                }

                // 应用透明度
                if (frameData.Alpha < 255)
                {
                    var colorMatrix = new System.Drawing.Imaging.ColorMatrix();
                    colorMatrix.Matrix33 = frameData.Alpha / 255.0f;
                    
                    var attributes = new System.Drawing.Imaging.ImageAttributes();
                    attributes.SetColorMatrix(colorMatrix);
                    
                    g.DrawImage(bitmap, new Rectangle(drawPos, bitmap.Size), 
                        0, 0, bitmap.Width, bitmap.Height, GraphicsUnit.Pixel, attributes);
                }
                else
                {
                    g.DrawImage(bitmap, drawPos);
                }
            }
            finally
            {
                g.Transform = oldTransform;
            }
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 动画面板绘制事件
        /// </summary>
        private void AnimationPanel_Paint(object sender, PaintEventArgs e)
        {
            var g = e.Graphics;
            g.InterpolationMode = InterpolationMode.NearestNeighbor;
            g.SmoothingMode = SmoothingMode.None;
            
            // 清除背景
            g.Clear(Color.White);
            
            if (_currentAnimation.HasValue)
            {
                DrawCurrentFrame(g);
            }
            else
            {
                // 绘制占位符
                var text = "未加载动画";
                var font = new Font("Microsoft YaHei", 12);
                var textSize = g.MeasureString(text, font);
                var textPos = new PointF(
                    (_animationPanel.Width - textSize.Width) / 2,
                    (_animationPanel.Height - textSize.Height) / 2);
                
                g.DrawString(text, font, Brushes.Gray, textPos);
            }
        }

        /// <summary>
        /// 播放按钮点击事件
        /// </summary>
        private void PlayButton_Click(object sender, EventArgs e)
        {
            if (_isPlaying)
            {
                Pause();
            }
            else
            {
                Play();
            }
        }

        /// <summary>
        /// 停止按钮点击事件
        /// </summary>
        private void StopButton_Click(object sender, EventArgs e)
        {
            Stop();
        }

        /// <summary>
        /// 帧进度条值改变事件
        /// </summary>
        private void FrameTrackBar_ValueChanged(object sender, EventArgs e)
        {
            _currentFrameIndex = _frameTrackBar.Value;
            UpdateFrameInfo();
            UpdateTimerInterval();
            _animationPanel.Invalidate();
        }

        /// <summary>
        /// 速度进度条值改变事件
        /// </summary>
        private void SpeedTrackBar_ValueChanged(object sender, EventArgs e)
        {
            PlaybackSpeed = _speedTrackBar.Value / 100.0f;
            
            // 更新速度显示
            var speedDisplay = _controlPanel.Controls["SpeedDisplay"] as Label;
            if (speedDisplay != null)
            {
                speedDisplay.Text = string.Format("{0:F1}x", _playbackSpeed);
            }
        }

        /// <summary>
        /// 动画定时器事件
        /// </summary>
        private void AnimationTimer_Tick(object sender, EventArgs e)
        {
            if (!_currentAnimation.HasValue)
                return;

            var animation = _currentAnimation.Value;
            
            // 移动到下一帧
            _currentFrameIndex++;
            
            // 处理播放模式
            switch (animation.GetPlayMode())
            {
                case Enums.AnimationPlayMode.Loop:
                    if (_currentFrameIndex >= animation.FrameCount)
                    {
                        _currentFrameIndex = 0;
                    }
                    break;
                    
                case Enums.AnimationPlayMode.Once:
                    if (_currentFrameIndex >= animation.FrameCount)
                    {
                        Stop();
                        return;
                    }
                    break;
                    
                case Enums.AnimationPlayMode.PingPong:
                    // 简化的乒乓模式实现
                    if (_currentFrameIndex >= animation.FrameCount)
                    {
                        _currentFrameIndex = Math.Max(0, animation.FrameCount - 2);
                    }
                    break;
            }
            
            // 更新界面
            _frameTrackBar.Value = _currentFrameIndex;
            UpdateFrameInfo();
            UpdateTimerInterval();
            _animationPanel.Invalidate();
        }

        #endregion

        #region 资源清理

        /// <summary>
        /// 释放资源
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                if (_animationTimer != null)
                    _animationTimer.Dispose();
                ClearFrameCache();
            }
            
            base.Dispose(disposing);
        }

        #endregion
    }
}
