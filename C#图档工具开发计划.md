# C# 图档导出导入工具开发计划

## 项目概述
基于CrossGate项目源码分析，开发一个独立的C#图档导出导入工具，具备图档与动画查看功能。

## 开发进度总览
- [x] 阶段一：核心数据结构设计 (1-2周)
- [x] 阶段二：文件读取引擎 (2-3周)
- [x] 阶段三：图像处理模块 (1-2周)
- [x] 阶段四：用户界面设计 (2-3周)
- [ ] 阶段五：导出导入功能 (2周)
- [ ] 阶段六：高级功能 (2-3周)
- [ ] 阶段七：优化和测试 (1-2周)

---

## 阶段一：核心数据结构设计 (1-2周)

### 1.1 基础数据结构定义
- [x] 创建项目结构和解决方案
- [x] 定义RDHeader结构体
  ```csharp
  public struct RDHeader
  {
      public byte[] Id;           // "RD" 标识
      public byte CompressFlag;   // 压缩标志
      public uint Width;          // 图像宽度
      public uint Height;         // 图像高度
      public uint Size;           // 数据大小
  }
  ```
- [x] 定义AdrnBin结构体
  ```csharp
  public struct AdrnBin
  {
      public uint BitmapNo;       // 位图编号
      public uint Address;        // 文件地址
      public uint Size;           // 数据大小
      public short XOffset;       // X偏移
      public short YOffset;       // Y偏移
      public ushort Width;        // 宽度
      public ushort Height;       // 高度
      public short BinMode;       // 文件模式
      public MapAttr Attributes;  // 地图属性
  }
  ```
- [x] 定义SpriteData结构体
  ```csharp
  public struct SpriteData
  {
      public uint SprNo;          // 精灵编号
      public uint Offset;         // 文件偏移
      public uint AnimSize;       // 动画数量
      public byte Format;         // 格式版本
      public List<AnimList> Animations; // 动画列表
  }
  ```
- [x] 定义AnimList和AnimFrame结构体
- [x] 定义MapAttr地图属性结构体

### 1.2 枚举类型定义
- [x] 定义BinMode枚举
  ```csharp
  public enum BinMode
  {
      Normal = 0,     // 基础版本
      Ex = 1,         // 扩展版本
      Ver2 = 2,       // 第二版本
      Joy = 3,        // Joy版本
      JoyCh = 4       // Joy中文版本
  }
  ```
- [x] 定义ImageFormat枚举
- [x] 定义CompressionLevel枚举
- [x] 定义ExportFormat枚举

### 1.3 常量定义
- [x] 定义文件格式常量
- [x] 定义压缩标志常量
- [x] 定义默认配置常量
- [x] 创建配置管理类

---

## 阶段二：文件读取引擎 (2-3周)

### 2.1 图档文件读取器
- [x] 创建GraphicFileReader类基础框架
- [x] 实现多版本文件流管理
  ```csharp
  private Dictionary<BinMode, FileStream> _graphicStreams;
  private Dictionary<BinMode, FileStream> _infoStreams;
  ```
- [x] 实现Initialize方法
  - [x] 文件存在性检查
  - [x] 文件格式验证
  - [x] 多版本文件打开
- [x] 实现ReadRawImageData方法
  - [x] 根据图像编号查找地址信息
  - [x] 定位到正确的文件位置
  - [x] 读取压缩数据
- [x] 实现GetImageInfo方法
- [x] 添加错误处理和资源释放

### 2.2 地址信息读取器
- [x] 创建AddressInfoReader类
- [x] 实现地址信息文件解析
  - [x] 读取ADRNBIN结构数据
  - [x] 处理不同版本的格式差异
  - [x] 建立图像编号到地址的映射
- [x] 实现地址信息缓存机制
- [x] 添加地址信息验证功能

### 2.3 解压缩引擎
- [x] 创建RDDecompressor类
- [x] 实现Decompress静态方法
  - [x] 解析RD文件头
  - [x] 处理压缩标志判断
  - [x] 实现压缩数据解压算法
- [x] 实现ProcessCompressedBlock方法
  - [x] 处理重复数据压缩
  - [x] 处理零值压缩
  - [x] 处理大数据块压缩
- [x] 实现ProcessUncompressedBlock方法
- [x] 实现CreateBitmap方法
  - [x] 处理8位索引色位图
  - [x] 处理调色板应用
  - [x] 处理24位真彩色位图

### 2.4 精灵动画读取器
- [x] 创建SpriteFileReader类
- [x] 实现LoadSpriteFiles方法
  - [x] 读取SPRADRN索引文件
  - [x] 解析精灵数据结构
  - [x] 建立精灵编号映射
- [x] 实现GetSpriteData方法
- [x] 实现GetAnimationFrames方法
  - [x] 读取动画列表数据
  - [x] 解析帧列表信息
  - [x] 处理动画参数
- [x] 实现动画数据缓存机制

---

## 阶段三：图像处理模块 (1-2周)

### 3.1 图像转换器
- [x] 创建ImageConverter类
- [x] 实现ConvertToBitmap方法
  - [x] 处理原始图像数据转换
  - [x] 应用调色板到位图
  - [x] 处理透明色设置
- [x] 实现ConvertToRawData方法
  - [x] 位图转换为原始数据
  - [x] 提取调色板信息
  - [x] 处理颜色量化
- [x] 实现SaveAsStandardFormat方法
  - [x] 支持PNG格式导出
  - [x] 支持BMP格式导出
  - [x] 支持JPG格式导出
  - [x] 支持TGA格式导出（基础框架）
- [x] 实现LoadStandardFormat方法
- [x] 实现ResizeImage方法（新增功能）

### 3.2 调色板管理器
- [x] 创建PaletteManager类
- [x] 实现ExtractPalette方法
  - [x] 从图像数据提取调色板
  - [x] 处理调色板格式转换
- [x] 实现ApplyPalette方法
- [x] 实现GenerateOptimalPalette方法
  - [x] 颜色量化算法
  - [x] 调色板优化算法
- [x] 实现调色板编辑功能
- [x] 实现QuantizeImage方法（新增功能）
- [x] 实现调色板混合功能（新增功能）

### 3.3 图像验证器
- [x] 创建ImageValidator类
- [x] 实现图像格式验证
- [x] 实现图像完整性检查
- [x] 实现尺寸限制验证
- [x] 实现调色板验证
- [x] 实现内存使用估算（新增功能）
- [x] 实现文件访问权限检查（新增功能）

---

## 阶段四：用户界面设计 (2-3周)

### 4.1 主窗口设计
- [x] 创建MainForm窗体
- [x] 设计主菜单栏
  - [x] 文件菜单 (打开、保存、退出)
  - [x] 编辑菜单 (复制、粘贴、删除)
  - [x] 视图菜单 (缩放、网格、属性)
  - [x] 工具菜单 (批量操作、设置)
  - [x] 帮助菜单 (关于、帮助文档)
- [x] 设计工具栏
  - [x] 常用操作按钮
  - [x] 缩放控制
  - [x] 视图切换
- [x] 设计状态栏
  - [x] 进度显示
  - [x] 状态信息
  - [x] 图像信息显示
- [x] 实现窗口布局管理
- [x] 实现主要事件处理方法

### 4.2 图档浏览器
- [x] 创建GraphicBrowser用户控件
- [x] 设计文件树视图
  - [x] 按版本分组显示
  - [x] 按类型分组显示
  - [x] 搜索过滤功能
- [x] 设计列表视图（替代缩略图视图）
  - [x] 详细信息显示
  - [x] 多列排序
  - [x] 分类显示功能
- [x] 实现LoadGraphics方法
- [x] 实现图像选择事件处理
- [x] 实现搜索功能
- [x] 实现视图模式切换

### 4.3 图像预览器
- [x] 创建ImagePreview用户控件
- [x] 实现图像显示功能
  - [x] 缩放显示
  - [x] 鼠标滚轮缩放
  - [x] 适应窗口大小
- [x] 实现属性显示面板
  - [x] 图像基本信息
  - [x] 文件信息
  - [x] 背景模式切换
- [x] 实现图像导出功能
  - [x] 标准格式导出
  - [x] 缩放控制
- [x] 添加导出预览功能

### 4.4 动画播放器
- [x] 创建AnimationPlayer用户控件
- [x] 实现动画播放控制
  - [x] 播放/暂停按钮
  - [x] 帧进度条
  - [x] 速度控制
- [x] 实现LoadAnimation方法
- [x] 实现PlayAnimation方法
- [x] 实现PauseAnimation方法
- [x] 实现SetAnimationSpeed方法
- [x] 添加帧缓存功能
- [x] 实现动画渲染功能

---

## 阶段五：导出导入功能 (2周)

### 5.1 批量导出器
- [ ] 创建BatchExporter类
- [ ] 定义ExportOptions配置类
  ```csharp
  public class ExportOptions
  {
      public ImageFormat Format { get; set; }
      public bool IncludePalette { get; set; }
      public bool CreateSubfolders { get; set; }
      public string NamingPattern { get; set; }
  }
  ```
- [ ] 实现ExportAllGraphics方法
  - [ ] 异步导出处理
  - [ ] 进度报告功能
  - [ ] 错误处理和日志
- [ ] 实现ExportByRange方法
- [ ] 实现ExportAnimations方法
  - [ ] 导出动画帧
  - [ ] 导出动画配置
  - [ ] 生成动画预览
- [ ] 实现导出进度界面
- [ ] 添加导出设置对话框

### 5.2 图像导入器
- [ ] 创建ImageImporter类
- [ ] 定义ImportOptions配置类
- [ ] 实现ImportImage方法
  - [ ] 图像格式检查
  - [ ] 尺寸验证
  - [ ] 格式转换
- [ ] 实现ImportBatch方法
- [ ] 实现CompressImage方法
  - [ ] 图像压缩算法
  - [ ] 调色板优化
- [ ] 实现导入预览功能
- [ ] 添加导入设置对话框
- [ ] 实现导入进度显示

### 5.3 文件管理器
- [ ] 创建FileManager类
- [ ] 实现文件备份功能
- [ ] 实现文件恢复功能
- [ ] 实现文件完整性检查
- [ ] 添加文件操作日志

---

## 阶段六：高级功能 (2-3周)

### 6.1 图档编辑器
- [ ] 创建GraphicEditor窗体
- [ ] 实现基础编辑功能
  - [ ] 画笔工具
  - [ ] 选择工具
  - [ ] 填充工具
- [ ] 实现LoadGraphic方法
- [ ] 实现SaveGraphic方法
- [ ] 实现ResizeImage方法
- [ ] 实现EditPalette方法
- [ ] 添加撤销/重做功能
- [ ] 实现图层管理

### 6.2 动画编辑器
- [ ] 创建AnimationEditor窗体
- [ ] 实现帧管理功能
  - [ ] 添加帧
  - [ ] 删除帧
  - [ ] 调整帧顺序
- [ ] 实现AddFrame方法
- [ ] 实现RemoveFrame方法
- [ ] 实现SetFrameDelay方法
- [ ] 实现PreviewAnimation方法
- [ ] 添加关键帧编辑
- [ ] 实现动画导出

### 6.3 搜索和过滤引擎
- [ ] 创建GraphicSearchEngine类
- [ ] 实现SearchBySize方法
- [ ] 实现SearchByPalette方法
- [ ] 实现SearchByContent方法
  - [ ] 图像相似度算法
  - [ ] 模板匹配功能
- [ ] 实现SearchUnused方法
- [ ] 添加高级搜索界面
- [ ] 实现搜索结果管理

### 6.4 插件系统
- [ ] 设计插件接口
- [ ] 实现插件加载器
- [ ] 创建示例插件
- [ ] 添加插件管理界面

---

## 阶段七：优化和测试 (1-2周)

### 7.1 性能优化
- [ ] 实现图像缓存机制
  - [ ] LRU缓存算法
  - [ ] 内存使用监控
  - [ ] 缓存清理策略
- [ ] 优化大文件读取性能
  - [ ] 异步文件操作
  - [ ] 分块读取
  - [ ] 内存映射文件
- [ ] 实现多线程处理
  - [ ] 后台任务队列
  - [ ] 线程池管理
  - [ ] UI线程同步
- [ ] 内存使用优化
  - [ ] 对象池模式
  - [ ] 及时释放资源
  - [ ] 垃圾回收优化

### 7.2 错误处理和日志
- [ ] 实现全局异常处理
- [ ] 添加详细日志记录
- [ ] 实现错误恢复机制
- [ ] 创建用户友好的错误提示
- [ ] 添加崩溃报告功能

### 7.3 测试和验证
- [ ] 创建单元测试
  - [ ] 数据结构测试
  - [ ] 文件读取测试
  - [ ] 图像处理测试
- [ ] 创建集成测试
  - [ ] 完整流程测试
  - [ ] 性能测试
  - [ ] 兼容性测试
- [ ] 进行用户测试
- [ ] 修复发现的问题

### 7.4 文档和发布
- [ ] 编写用户手册
- [ ] 创建开发文档
- [ ] 准备发布包
- [ ] 创建安装程序
- [ ] 发布第一个版本

---

## 技术栈和依赖

### 主要技术栈
- [ ] .NET Framework 4.8 或 .NET 6+
- [ ] System.Drawing 或 ImageSharp
- [ ] System.Windows.Forms 或 WPF
- [ ] System.IO.Compression

### 可选增强库
- [ ] DevExpress 或 Telerik (UI组件)
- [ ] FFMpegCore (视频导出)
- [ ] Newtonsoft.Json (配置文件)
- [ ] NLog (日志记录)

---

## 项目文件结构

```
GraphicTool/
├── Core/
│   ├── DataStructures/     # 数据结构定义
│   ├── FileReaders/        # 文件读取器
│   ├── Compression/        # 压缩解压缩
│   └── ImageProcessing/    # 图像处理
├── UI/
│   ├── MainForm/          # 主窗口
│   ├── Viewers/           # 查看器控件
│   ├── Editors/           # 编辑器窗体
│   └── Dialogs/           # 对话框
├── Utils/
│   ├── Converters/        # 转换器
│   ├── Validators/        # 验证器
│   └── Helpers/           # 辅助类
└── Tests/
    ├── UnitTests/         # 单元测试
    └── IntegrationTests/  # 集成测试
```

---

## 开发注意事项

1. **版本控制**: 每完成一个子任务就提交代码
2. **代码规范**: 遵循C#编码规范和最佳实践
3. **错误处理**: 每个方法都要有适当的错误处理
4. **性能考虑**: 处理大文件时注意内存使用
5. **用户体验**: 长时间操作要有进度提示
6. **兼容性**: 考虑不同版本文件的兼容性
7. **测试**: 每个功能都要有对应的测试用例

---

## 里程碑检查点

- [x] **Week 2**: 核心数据结构完成，可以解析基本文件格式
- [x] **Week 5**: 文件读取引擎完成，可以读取和解压图像
- [x] **Week 7**: 图像处理模块完成，可以转换和显示图像
- [x] **Week 10**: 基础UI完成，可以浏览和预览图档
- [ ] **Week 12**: 导出导入功能完成，可以批量处理文件
- [ ] **Week 15**: 高级功能完成，具备完整的编辑能力
- [ ] **Week 17**: 优化测试完成，可以发布第一个版本

每个里程碑都应该有可演示的功能，确保项目按计划推进。
