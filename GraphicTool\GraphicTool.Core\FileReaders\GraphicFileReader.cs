using System;
using System.Collections.Generic;
using System.IO;
using GraphicTool.Core.DataStructures;

namespace GraphicTool.Core.FileReaders
{
    /// <summary>
    /// 图档文件读取器
    /// 负责读取和管理多版本的图档文件
    /// </summary>
    public class GraphicFileReader : IDisposable
    {
        #region 私有字段

        /// <summary>
        /// 图档文件流字典，按BinMode索引
        /// </summary>
        private Dictionary<Enums.BinMode, FileStream> _graphicStreams;

        /// <summary>
        /// 图档信息文件流字典，按BinMode索引
        /// </summary>
        private Dictionary<Enums.BinMode, FileStream> _infoStreams;

        /// <summary>
        /// 地址信息缓冲区
        /// </summary>
        private List<AdrnBin> _addressBuffer;

        /// <summary>
        /// 是否已初始化
        /// </summary>
        private bool _isInitialized;

        /// <summary>
        /// 是否已释放资源
        /// </summary>
        private bool _disposed;

        #endregion

        #region 公共属性

        /// <summary>
        /// 是否已初始化
        /// </summary>
        public bool IsInitialized
        {
            get { return _isInitialized; }
        }

        /// <summary>
        /// 已加载的图像数量
        /// </summary>
        public int LoadedImageCount
        {
            get { return _addressBuffer != null ? _addressBuffer.Count : 0; }
        }

        /// <summary>
        /// 支持的文件模式列表
        /// </summary>
        public List<Enums.BinMode> SupportedModes
        {
            get
            {
                var modes = new List<Enums.BinMode>();
                if (_graphicStreams != null)
                {
                    foreach (var kvp in _graphicStreams)
                    {
                        if (kvp.Value != null)
                            modes.Add(kvp.Key);
                    }
                }
                return modes;
            }
        }

        #endregion

        #region 构造函数和初始化

        /// <summary>
        /// 构造函数
        /// </summary>
        public GraphicFileReader()
        {
            _graphicStreams = new Dictionary<Enums.BinMode, FileStream>();
            _infoStreams = new Dictionary<Enums.BinMode, FileStream>();
            _addressBuffer = new List<AdrnBin>();
            _isInitialized = false;
            _disposed = false;
        }

        /// <summary>
        /// 初始化图档文件读取器
        /// </summary>
        /// <param name="graphicPaths">图档文件路径数组，按BinMode顺序</param>
        /// <param name="infoPaths">图档信息文件路径数组，按BinMode顺序</param>
        /// <returns>初始化是否成功</returns>
        public bool Initialize(string[] graphicPaths, string[] infoPaths)
        {
            try
            {
                if (_isInitialized)
                {
                    throw new InvalidOperationException("GraphicFileReader已经初始化");
                }

                if (graphicPaths == null || infoPaths == null)
                {
                    throw new ArgumentNullException("文件路径数组不能为null");
                }

                if (graphicPaths.Length != infoPaths.Length)
                {
                    throw new ArgumentException("图档文件和信息文件数量不匹配");
                }

                // 打开图档文件
                for (int i = 0; i < graphicPaths.Length && i < (int)Enums.BinMode.Max; i++)
                {
                    var mode = (Enums.BinMode)i;
                    
                    if (!string.IsNullOrEmpty(graphicPaths[i]) && File.Exists(graphicPaths[i]))
                    {
                        var graphicStream = new FileStream(graphicPaths[i], FileMode.Open, FileAccess.Read, FileShare.Read);
                        _graphicStreams[mode] = graphicStream;
                    }
                }

                // 打开图档信息文件并读取地址信息
                for (int i = 0; i < infoPaths.Length && i < (int)Enums.BinMode.Max; i++)
                {
                    var mode = (Enums.BinMode)i;
                    
                    if (!string.IsNullOrEmpty(infoPaths[i]) && File.Exists(infoPaths[i]))
                    {
                        var infoStream = new FileStream(infoPaths[i], FileMode.Open, FileAccess.Read, FileShare.Read);
                        _infoStreams[mode] = infoStream;
                        
                        // 读取地址信息
                        LoadAddressInfo(infoStream, mode);
                    }
                }

                // 检查是否至少有一个文件成功打开
                if (_graphicStreams.Count == 0)
                {
                    throw new FileNotFoundException("没有找到有效的图档文件");
                }

                _isInitialized = true;
                return true;
            }
            catch (Exception ex)
            {
                // 清理已打开的文件
                Cleanup();
                throw new InvalidOperationException("初始化图档文件读取器失败: " + ex.Message, ex);
            }
        }

        /// <summary>
        /// 使用默认文件名初始化
        /// </summary>
        /// <param name="basePath">基础路径</param>
        /// <returns>初始化是否成功</returns>
        public bool InitializeWithDefaults(string basePath)
        {
            if (string.IsNullOrEmpty(basePath))
            {
                throw new ArgumentNullException("basePath");
            }

            var graphicPaths = new string[(int)Enums.BinMode.Max];
            var infoPaths = new string[(int)Enums.BinMode.Max];

            for (int i = 0; i < (int)Enums.BinMode.Max; i++)
            {
                graphicPaths[i] = Path.Combine(basePath, Constants.DEFAULT_GRAPHIC_NAMES[i]);
                infoPaths[i] = Path.Combine(basePath, Constants.DEFAULT_GRAPHIC_INFO_NAMES[i]);
            }

            return Initialize(graphicPaths, infoPaths);
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 读取原始图像数据
        /// </summary>
        /// <param name="graphicNo">图像编号</param>
        /// <returns>原始图像数据</returns>
        public byte[] ReadRawImageData(int graphicNo)
        {
            if (!_isInitialized)
            {
                throw new InvalidOperationException("GraphicFileReader未初始化");
            }

            if (graphicNo < 0 || graphicNo >= _addressBuffer.Count)
            {
                throw new ArgumentOutOfRangeException("graphicNo", "图像编号超出范围");
            }

            var adrnBin = _addressBuffer[graphicNo];
            var mode = adrnBin.GetBinMode();

            if (!_graphicStreams.ContainsKey(mode) || _graphicStreams[mode] == null)
            {
                throw new InvalidOperationException("对应的图档文件未打开: " + mode);
            }

            var stream = _graphicStreams[mode];

            try
            {
                // 定位到文件中的位置
                stream.Seek(adrnBin.Address, SeekOrigin.Begin);

                // 读取压缩数据
                var buffer = new byte[adrnBin.Size];
                var bytesRead = stream.Read(buffer, 0, buffer.Length);

                if (bytesRead != adrnBin.Size)
                {
                    throw new InvalidDataException("读取的数据大小不匹配");
                }

                return buffer;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("读取图像数据失败: " + ex.Message, ex);
            }
        }

        /// <summary>
        /// 解压缩图像
        /// </summary>
        /// <param name="compressedData">压缩数据</param>
        /// <returns>位图对象</returns>
        public System.Drawing.Bitmap DecompressImage(byte[] compressedData)
        {
            if (compressedData == null)
            {
                throw new ArgumentNullException("compressedData");
            }

            try
            {
                // 使用RD解压缩器解压数据
                var decompressedData = Compression.RDDecompressor.Decompress(compressedData);

                // 解析头部获取尺寸信息
                var header = ParseRDHeader(compressedData);

                // 创建位图
                byte[] palette = null;
                byte[] imageData = decompressedData;

                if (header.HasPalette)
                {
                    palette = new byte[Constants.PALETTE_BYTE_SIZE];
                    Array.Copy(decompressedData, 0, palette, 0, Constants.PALETTE_BYTE_SIZE);

                    imageData = new byte[decompressedData.Length - Constants.PALETTE_BYTE_SIZE];
                    Array.Copy(decompressedData, Constants.PALETTE_BYTE_SIZE, imageData, 0, imageData.Length);
                }

                return Compression.RDDecompressor.CreateBitmap(imageData, (int)header.Width, (int)header.Height, palette);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("解压缩图像失败: " + ex.Message, ex);
            }
        }

        /// <summary>
        /// 获取图像信息
        /// </summary>
        /// <param name="graphicNo">图像编号</param>
        /// <returns>地址信息</returns>
        public AdrnBin GetImageInfo(int graphicNo)
        {
            if (!_isInitialized)
            {
                throw new InvalidOperationException("GraphicFileReader未初始化");
            }

            if (graphicNo < 0 || graphicNo >= _addressBuffer.Count)
            {
                throw new ArgumentOutOfRangeException("graphicNo", "图像编号超出范围");
            }

            return _addressBuffer[graphicNo];
        }

        /// <summary>
        /// 获取图像数量
        /// </summary>
        /// <returns>图像数量</returns>
        public int GetImageCount()
        {
            return LoadedImageCount;
        }

        /// <summary>
        /// 检查图像是否存在
        /// </summary>
        /// <param name="graphicNo">图像编号</param>
        /// <returns>是否存在</returns>
        public bool ImageExists(int graphicNo)
        {
            return _isInitialized && graphicNo >= 0 && graphicNo < _addressBuffer.Count;
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 加载地址信息
        /// </summary>
        /// <param name="infoStream">信息文件流</param>
        /// <param name="mode">文件模式</param>
        private void LoadAddressInfo(FileStream infoStream, Enums.BinMode mode)
        {
            infoStream.Seek(0, SeekOrigin.Begin);
            
            var buffer = new byte[AdrnBin.SizeOf];
            
            while (infoStream.Read(buffer, 0, buffer.Length) == buffer.Length)
            {
                var adrnBin = BytesToAdrnBin(buffer);
                adrnBin.SetBinMode(mode);
                
                // 验证数据有效性
                if (adrnBin.IsValid)
                {
                    _addressBuffer.Add(adrnBin);
                }
            }
        }

        /// <summary>
        /// 字节数组转换为AdrnBin结构
        /// </summary>
        /// <param name="buffer">字节数组</param>
        /// <returns>AdrnBin结构</returns>
        private AdrnBin BytesToAdrnBin(byte[] buffer)
        {
            return AddressInfoReader.BytesToAdrnBin(buffer);
        }

        /// <summary>
        /// 解析RD文件头部
        /// </summary>
        /// <param name="data">数据</param>
        /// <returns>RD头部结构</returns>
        private RDHeader ParseRDHeader(byte[] data)
        {
            var header = new RDHeader();

            if (data.Length >= RDHeader.SizeOf)
            {
                // 读取标识符
                header.Id = new byte[2];
                header.Id[0] = data[0];
                header.Id[1] = data[1];

                // 读取压缩标志
                header.CompressFlag = data[2];

                // 读取尺寸信息
                header.Width = BitConverter.ToUInt32(data, 3);
                header.Height = BitConverter.ToUInt32(data, 7);
                header.Size = BitConverter.ToUInt32(data, 11);
            }

            return header;
        }

        /// <summary>
        /// 清理资源
        /// </summary>
        private void Cleanup()
        {
            if (_graphicStreams != null)
            {
                foreach (var stream in _graphicStreams.Values)
                {
                    if (stream != null)
                    {
                        stream.Dispose();
                    }
                }
                _graphicStreams.Clear();
            }

            if (_infoStreams != null)
            {
                foreach (var stream in _infoStreams.Values)
                {
                    if (stream != null)
                    {
                        stream.Dispose();
                    }
                }
                _infoStreams.Clear();
            }

            if (_addressBuffer != null)
            {
                _addressBuffer.Clear();
            }

            _isInitialized = false;
        }

        #endregion

        #region IDisposable实现

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        /// <param name="disposing">是否正在释放托管资源</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    Cleanup();
                }
                _disposed = true;
            }
        }

        /// <summary>
        /// 析构函数
        /// </summary>
        ~GraphicFileReader()
        {
            Dispose(false);
        }

        #endregion
    }
}
