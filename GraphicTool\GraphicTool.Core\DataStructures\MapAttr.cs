using System;
using System.Runtime.InteropServices;

namespace GraphicTool.Core.DataStructures
{
    /// <summary>
    /// 地图属性结构
    /// 对应原始C++代码中的MAP_ATTR结构
    /// 用于存储图像的地图相关属性
    /// </summary>
    [StructLayout(LayoutKind.Sequential, Pack = 1)]
    public struct MapAttr
    {
        /// <summary>
        /// 地图属性标志
        /// </summary>
        public uint Flags;

        /// <summary>
        /// 扩展属性1
        /// </summary>
        public uint ExtAttr1;

        /// <summary>
        /// 扩展属性2
        /// </summary>
        public uint ExtAttr2;

        /// <summary>
        /// 保留字段
        /// </summary>
        public uint Reserved;

        /// <summary>
        /// 检查是否可通行
        /// </summary>
        public bool IsPassable
        {
            get { return (Flags & 0x01) == 0; }
            set 
            { 
                if (value)
                    Flags &= ~0x01u;
                else
                    Flags |= 0x01;
            }
        }

        /// <summary>
        /// 检查是否是障碍物
        /// </summary>
        public bool IsObstacle
        {
            get { return (Flags & 0x01) != 0; }
            set { IsPassable = !value; }
        }

        /// <summary>
        /// 检查是否是传送点
        /// </summary>
        public bool IsTeleport
        {
            get { return (Flags & 0x02) != 0; }
            set 
            { 
                if (value)
                    Flags |= 0x02;
                else
                    Flags &= ~0x02u;
            }
        }

        /// <summary>
        /// 检查是否是特殊区域
        /// </summary>
        public bool IsSpecialArea
        {
            get { return (Flags & 0x04) != 0; }
            set 
            { 
                if (value)
                    Flags |= 0x04;
                else
                    Flags &= ~0x04u;
            }
        }

        /// <summary>
        /// 检查是否有动画效果
        /// </summary>
        public bool HasAnimation
        {
            get { return (Flags & 0x08) != 0; }
            set 
            { 
                if (value)
                    Flags |= 0x08;
                else
                    Flags &= ~0x08u;
            }
        }

        /// <summary>
        /// 创建默认的地图属性
        /// </summary>
        /// <returns>默认地图属性</returns>
        public static MapAttr CreateDefault()
        {
            return new MapAttr
            {
                Flags = 0,
                ExtAttr1 = 0,
                ExtAttr2 = 0,
                Reserved = 0
            };
        }

        /// <summary>
        /// 创建具有指定属性的地图属性
        /// </summary>
        /// <param name="passable">是否可通行</param>
        /// <param name="teleport">是否是传送点</param>
        /// <param name="specialArea">是否是特殊区域</param>
        /// <param name="hasAnimation">是否有动画</param>
        /// <returns>地图属性</returns>
        public static MapAttr Create(bool passable = true, bool teleport = false, 
            bool specialArea = false, bool hasAnimation = false)
        {
            var attr = CreateDefault();
            attr.IsPassable = passable;
            attr.IsTeleport = teleport;
            attr.IsSpecialArea = specialArea;
            attr.HasAnimation = hasAnimation;
            return attr;
        }

        /// <summary>
        /// 获取结构体的字节大小
        /// </summary>
        public static int SizeOf { get { return Marshal.SizeOf(typeof(MapAttr)); } }

        public override string ToString()
        {
            return string.Format("MapAttr: Flags=0x{0:X8}, Passable={1}, Teleport={2}, Special={3}, Anim={4}",
                Flags, IsPassable, IsTeleport, IsSpecialArea, HasAnimation);
        }
    }
}
