using System;
using System.Runtime.InteropServices;
using GraphicTool.Core.DataStructures;

namespace GraphicTool.Tests
{
    /// <summary>
    /// 数据结构测试类
    /// </summary>
    public static class DataStructureTests
    {
        /// <summary>
        /// 运行所有数据结构测试
        /// </summary>
        public static void RunAllTests()
        {
            Console.WriteLine("=== 开始数据结构测试 ===");
            Console.WriteLine();

            TestRDHeader();
            TestAdrnBin();
            TestMapAttr();
            TestSpriteData();
            TestAnimList();
            TestAnimFrame();
            TestEnums();
            TestConstants();

            Console.WriteLine();
            Console.WriteLine("=== 所有测试完成 ===");
        }

        /// <summary>
        /// 测试RDHeader结构
        /// </summary>
        private static void TestRDHeader()
        {
            Console.WriteLine("测试 RDHeader 结构:");

            // 测试创建
            var header = RDHeader.Create(800, 600, 1024, true, true);
            Console.WriteLine("  创建: " + header);
            Console.WriteLine("  结构大小: " + RDHeader.SizeOf + " 字节");
            Console.WriteLine("  是否有效: " + header.IsValidHeader);
            Console.WriteLine("  是否压缩: " + header.IsCompressed);
            Console.WriteLine("  是否有调色板: " + header.HasPalette);

            // 测试无效头部
            var invalidHeader = new RDHeader { Id = new byte[] { (byte)'X', (byte)'Y' } };
            Console.WriteLine("  无效头部检测: " + (!invalidHeader.IsValidHeader));

            Console.WriteLine();
        }

        /// <summary>
        /// 测试AdrnBin结构
        /// </summary>
        private static void TestAdrnBin()
        {
            Console.WriteLine("测试 AdrnBin 结构:");

            // 测试创建
            var adrn = AdrnBin.Create(12345, 0x1000, 2048, 64, 64, Enums.BinMode.Ex, 10, 20);
            Console.WriteLine("  创建: " + adrn);
            Console.WriteLine("  结构大小: " + AdrnBin.SizeOf + " 字节");
            Console.WriteLine("  是否有效: " + adrn.IsValid);
            Console.WriteLine("  BinMode: " + adrn.GetBinMode());

            // 测试设置BinMode
            adrn.SetBinMode(Enums.BinMode.Joy);
            Console.WriteLine("  设置BinMode后: " + adrn.GetBinMode());

            Console.WriteLine();
        }

        /// <summary>
        /// 测试MapAttr结构
        /// </summary>
        private static void TestMapAttr()
        {
            Console.WriteLine("测试 MapAttr 结构:");

            // 测试创建
            var attr = MapAttr.Create(true, false, true, false);
            Console.WriteLine("  创建: " + attr);
            Console.WriteLine("  结构大小: " + MapAttr.SizeOf + " 字节");

            // 测试属性设置
            attr.IsObstacle = true;
            attr.IsTeleport = true;
            Console.WriteLine("  设置属性后: " + attr);

            Console.WriteLine();
        }

        /// <summary>
        /// 测试SpriteData结构
        /// </summary>
        private static void TestSpriteData()
        {
            Console.WriteLine("测试 SpriteData 结构:");

            // 测试创建
            var sprite = SpriteData.Create(100, 0x2000, 2);
            Console.WriteLine("  创建: " + sprite);
            Console.WriteLine("  结构大小: " + SpriteData.SizeOf + " 字节");
            Console.WriteLine("  是否有效: " + sprite.IsValid);

            // 测试添加动画
            var anim = AnimList.Create(0, 1);
            sprite.AddAnimation(anim);
            Console.WriteLine("  添加动画后: AnimCount=" + sprite.AnimSize);
            Console.WriteLine("  总大小: " + sprite.GetTotalSize() + " 字节");

            Console.WriteLine();
        }

        /// <summary>
        /// 测试AnimList结构
        /// </summary>
        private static void TestAnimList()
        {
            Console.WriteLine("测试 AnimList 结构:");

            // 测试创建
            var anim = AnimList.Create(2, 5, Enums.AnimationPlayMode.Loop, 3);
            Console.WriteLine("  创建: " + anim);
            Console.WriteLine("  结构大小: " + AnimList.SizeOf + " 字节");
            Console.WriteLine("  是否有效: " + anim.IsValid);

            // 测试添加帧
            var frame = AnimFrame.Create(1001, 150, 5, -5, 200);
            anim.AddFrame(frame);
            Console.WriteLine("  添加帧后: FrameCount=" + anim.FrameCount + ", Duration=" + anim.TotalDuration + "ms");
            Console.WriteLine("  总大小: " + anim.GetTotalSize() + " 字节");

            Console.WriteLine();
        }

        /// <summary>
        /// 测试AnimFrame结构
        /// </summary>
        private static void TestAnimFrame()
        {
            Console.WriteLine("测试 AnimFrame 结构:");

            // 测试创建
            var frame = AnimFrame.Create(2001, 200, 15, -10, 180);
            Console.WriteLine("  创建: " + frame);
            Console.WriteLine("  结构大小: " + AnimFrame.SizeOf + " 字节");
            Console.WriteLine("  是否有效: " + frame.IsValid);
            Console.WriteLine("  是否可见: " + frame.IsVisible);

            // 测试旋转和缩放
            frame.SetRotationDegrees(45.0f);
            frame.SetScale(1.5f, 0.8f);
            Console.WriteLine("  设置旋转45度: " + frame.GetRotationDegrees().ToString("F1") + "度");
            Console.WriteLine("  设置缩放: X=" + frame.GetScaleXRatio().ToString("F2") + ", Y=" + frame.GetScaleYRatio().ToString("F2"));

            // 测试特效
            frame.SetEffect(Constants.EFFECT_BLINK, true);
            frame.SetEffect(Constants.EFFECT_FADE, true);
            Console.WriteLine("  设置特效: Blink=" + frame.HasEffect(Constants.EFFECT_BLINK) + ", Fade=" + frame.HasEffect(Constants.EFFECT_FADE));

            Console.WriteLine();
        }

        /// <summary>
        /// 测试枚举
        /// </summary>
        private static void TestEnums()
        {
            Console.WriteLine("测试枚举:");

            Console.WriteLine("  BinMode.Max = " + (int)Enums.BinMode.Max);
            Console.WriteLine("  ImageFormat.PNG = " + Enums.ImageFormat.PNG);
            Console.WriteLine("  CompressionLevel.High = " + Enums.CompressionLevel.High);
            Console.WriteLine("  AnimationPlayMode.PingPong = " + Enums.AnimationPlayMode.PingPong);

            Console.WriteLine();
        }

        /// <summary>
        /// 测试常量
        /// </summary>
        private static void TestConstants()
        {
            Console.WriteLine("测试常量:");

            Console.WriteLine("  RD签名: " + System.Text.Encoding.ASCII.GetString(Constants.RD_SIGNATURE));
            Console.WriteLine("  最大图像数: " + Constants.MAX_GRAPHICS);
            Console.WriteLine("  默认缓存大小: " + Constants.DEFAULT_CACHE_SIZE_MB + "MB");
            Console.WriteLine("  工具版本: " + Constants.TOOL_VERSION);
            Console.WriteLine("  默认图档文件: " + Constants.DEFAULT_GRAPHIC_NAMES[0]);

            Console.WriteLine();
        }
    }
}
